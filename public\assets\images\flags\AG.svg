<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>AG</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#E2243B" offset="0%"></stop>
            <stop stop-color="#CC162C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-4" points="0 0 21 0 10.5 15"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFCF3C" offset="0%"></stop>
            <stop stop-color="#FECB2F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#1984D8" offset="0%"></stop>
            <stop stop-color="#1175C4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="AG">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <path d="M8.5,7 C8.5,7.73933629 8.90117129,8.38497401 9.49770903,8.73110833 M11.576137,8.68609748 C12.1317007,8.33077576 12.5,7.70839833 12.5,7" id="Oval-5" opacity="0.75"></path>
            <ellipse id="Oval-5" fill-opacity="0.5" fill="#FFFFFF" cx="10.5" cy="6.5" rx="1" ry="1.5"></ellipse>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <mask id="mask-5" fill="white">
                <use xlink:href="#path-4"></use>
            </mask>
            <use id="Mask" fill="url(#linearGradient-3)" xlink:href="#path-4"></use>
            <polygon id="Star-1" fill="url(#linearGradient-6)" mask="url(#mask-5)" points="10.5 8.25 8.77792455 10.1574579 8.90900974 7.59099026 6.3425421 7.72207545 8.25 6 5.99999999 3.99999993 8.90900963 4.49999993 8.49999999 1.49999999 10.5 3.99999993 12.5 1.49999999 12.0909905 4.49999993 15 4 12.75 6 14.6574579 7.72207545 12.0909903 7.59099026 12.2220754 10.1574579"></polygon>
            <rect id="Rectangle-2" fill="url(#linearGradient-7)" mask="url(#mask-5)" x="0" y="6" width="21" height="4"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-1)" mask="url(#mask-5)" x="0" y="10" width="21" height="5"></rect>
        </g>
    </g>
</svg>