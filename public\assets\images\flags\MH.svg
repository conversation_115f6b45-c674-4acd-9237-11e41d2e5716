<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>MH</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#064DAE" offset="0%"></stop>
            <stop stop-color="#003D91" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#F18D36" offset="0%"></stop>
            <stop stop-color="#DB761E" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="MH">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <polygon id="Rectangle-253" fill="url(#linearGradient-3)" points="21 0 21 4 0 15"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-1)" points="21 8 21 4 0 15"></polygon>
            <path d="M4.83882103,4.3717472 L4.5,2 L4.16117897,4.3717472 L3.25,3.33493649 L3.71780952,4.71780952 L2.33493649,4.25 L3.3717472,5.16117897 L1,5.5 L3.3717472,5.83882103 L2.33493649,6.75 L3.71780952,6.28219048 L3.25,7.66506351 L4.16117897,6.6282528 L4.5,9 L4.83882103,6.6282528 L5.75,7.66506351 L5.28219048,6.28219048 L6.66506351,6.75 L5.6282528,5.83882103 L8,5.5 L5.6282528,5.16117897 L6.66506351,4.25 L5.28219048,4.71780952 L5.75,3.33493649 L4.83882103,4.3717472 Z" id="Combined-Shape" fill="url(#linearGradient-1)"></path>
        </g>
    </g>
</svg>