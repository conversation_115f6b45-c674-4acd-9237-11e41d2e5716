<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>SC</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#1DBE4F" offset="0%"></stop>
            <stop stop-color="#159B3F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#0858B4" offset="0%"></stop>
            <stop stop-color="#013F87" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#ED3535" offset="0%"></stop>
            <stop stop-color="#D62828" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#08964F" offset="0%"></stop>
            <stop stop-color="#017B3E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFDD67" offset="0%"></stop>
            <stop stop-color="#FDD856" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="SC">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <polygon id="Rectangle-253" fill="url(#linearGradient-3)" points="0 0 7 0 0 15"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-4)" points="14 0 21 0 21 5 0 15"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-5)" points="21 15 21 10 0 15"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-6)" points="7 0 14 0 0 15"></polygon>
            <polygon id="Rectangle-253" fill="url(#linearGradient-1)" points="21 10 21 5 0 15"></polygon>
        </g>
    </g>
</svg>