<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>TL</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FF323E" offset="0%"></stop>
            <stop stop-color="#FD0D1B" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFCC51" offset="0%"></stop>
            <stop stop-color="#FFC63C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#0D0D0D" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="TL">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <polygon id="Rectangle-83" fill="url(#linearGradient-3)" points="0 0 13 7.5 0 15"></polygon>
            <polygon id="Rectangle-83" fill="url(#linearGradient-4)" points="0 0 9 7.5 0 15"></polygon>
            <polygon id="Star-8" fill="url(#linearGradient-1)" transform="translate(3.920454, 7.187336) rotate(-30.000000) translate(-3.920454, -7.187336) " points="3.92045417 8.26230472 2.61898766 9.1900867 3.09918549 7.6656181 1.81463712 6.7145503 3.41288221 6.70015886 3.92045417 5.18458466 4.42802612 6.70015886 6.02627121 6.7145503 4.74172284 7.6656181 5.22192067 9.1900867"></polygon>
        </g>
    </g>
</svg>