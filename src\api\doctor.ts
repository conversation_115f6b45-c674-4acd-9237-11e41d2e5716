
// Doctor API module: Handles doctor CRUD and status (is_active) logic
import apiClient from '@/src/utils/apiClient';
import { DOCTOR_ENDPOINTS } from '@/src/utils/apiRoutes';
import { DASHBOARD_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray, extractDoctorsResponseArray } from '@/src/utils/apiErrorHandler';

export interface Doctor {
    id: number;
    user_uuid?: string;
    first_name: string;
    last_name: string;
    email: string;
    username?: string;
    doctor_addresses?: string;
    role_id?: number;
    /**
     * Indicates if the doctor is active (true) or inactive (false).
     * Used for filtering, toggling, and displaying doctor status.
     */
    is_active?: boolean;
    is_verified?: boolean;
    status?: 'active' | 'deleted' | string;
    deleted_at?: string | null;
    created_at?: string;
    updated_at?: string;
}

export interface CreateDoctorInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
    /**
     * Set to true to create the doctor as active, false for inactive.
     */
    is_active?: boolean;
}

// List doctors
export const getAllDoctors = async (params: { page: number; limit: number; filter?: 'active' | 'deleted' }) => {
    try {
        const query = new URLSearchParams();
        query.append('page', String(params.page));
        query.append('limit', String(params.limit));
        if (params.filter) query.append('filter', params.filter);
        const url = `${DOCTOR_ENDPOINTS.GET_DOCTORS}?${query.toString()}`;
        const response = await apiClient.get(url);
        const raw = response.data?.data || {};
        return {
            doctors: raw.doctors || [],
            page: raw.page || 1,
            limit: raw.limit || params.limit,
            totalCount: raw.totalCount || 0,
            totalPages: raw.totalPages || 1,
        };
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load doctors'));
    }
};

// Get active doctors
export const getActiveDoctors = async (page = 1, limit = 10): Promise<Doctor[]> => {
    const result = await getAllDoctors({ page, limit, filter: 'active' });
    return result.doctors;
};

// Get deleted doctors
export const getDeletedDoctors = async (page = 1, limit = 10): Promise<Doctor[]> => {
    const result = await getAllDoctors({ page, limit, filter: 'deleted' });
    return result.doctors;
};

// Create doctor
export const createDoctor = async (doctorData: CreateDoctorInput): Promise<Doctor> => {
    try {
        // Use multipart/form-data to match backend expectations (curl used --form)
        const formData = new FormData();
        Object.keys(doctorData).forEach((key) => {
            const val: any = (doctorData as any)[key];
            // Skip undefined, null or empty-string values so backend won't validate absent fields
            if (val === undefined || val === null) return;
            if (typeof val === 'string' && val.trim() === '') return;
            // Convert boolean to string for form fields
            formData.append(key, typeof val === 'boolean' ? String(val) : val);
        });

        const response = await apiClient.post(DOCTOR_ENDPOINTS.CREATE_DOCTOR, formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
        });
        return extractApiResponseData<Doctor>(response, 'No data returned after creation');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create doctor'));
    }
};

// Update doctor
export const updateDoctor = async (doctorId: number, doctorData: Partial<CreateDoctorInput>): Promise<Doctor> => {
    try {
        // Send update as multipart/form-data to include fields like is_active consistently
        const formData = new FormData();
        Object.keys(doctorData).forEach((key) => {
            const val: any = (doctorData as any)[key];
            if (val === undefined || val === null) return;
            if (typeof val === 'string' && val.trim() === '') return;
            formData.append(key, typeof val === 'boolean' ? String(val) : val);
        });

        const response = await apiClient.patch(DOCTOR_ENDPOINTS.UPDATE_DOCTOR(doctorId.toString()), formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
        });
        return extractApiResponseData<Doctor>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update doctor'));
    }
};

// View one doctor
export const viewDoctor = async (doctorId: number): Promise<Doctor> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTOR(doctorId.toString()));
        return extractApiResponseData<Doctor>(response, 'No doctor data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch doctor'));
    }
};

// Delete doctor
export const deleteDoctor = async (doctorId: number): Promise<void> => {
    try {
        await apiClient.delete(DOCTOR_ENDPOINTS.DELETE_DOCTOR(doctorId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete doctor'));
    }
};

// Restore doctor (if soft delete is implemented)
export const restoreDoctor = async (doctorId: number): Promise<Doctor> => {
    try {
        console.log('Restoring doctor with ID:', doctorId);
        const response = await apiClient.put(DOCTOR_ENDPOINTS.RESTORE_DOCTOR(doctorId.toString()));
        console.log('Restore API Response:', response.data);
        return extractApiResponseData<Doctor>(response, 'No data returned after restoration');
    } catch (error: any) {
        console.error('Restore API Error:', error);
        throw new Error(extractApiErrorMessage(error, 'Could not restore doctor'));
    }
};

/**
 * Toggle the active status of a doctor (is_active field).
 * @param doctorId Doctor's ID
 * @param isActive Set to true to activate, false to deactivate
 * @returns Updated Doctor object
 */
export const toggleDoctorStatus = async (doctorId: number, isActive: boolean): Promise<Doctor> => {
    try {
        // Create form data for the request
        const formData = new FormData();
        formData.append('status', isActive ? 'activate' : 'deactivate');

        const response = await apiClient.patch(`/admin/${doctorId}/user-status`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        // Return the updated doctor object, merging API response
        if (response.data && response.status === 200) {
            return {
                id: doctorId,
                is_active: isActive,
                first_name: '',
                last_name: '',
                email: '',
                ...response.data.data || {}
            } as Doctor;
        }

        throw new Error('Invalid response from server');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not toggle doctor status'));
    }
};

// Dashboard stats
export interface DashboardStats {
    doctors: number;
    patients: number;
    specialist: number;
    pendingPatients: number;
    incompletePatients: number;
    completePatients: number;
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
    try {
        const response = await apiClient.get(DASHBOARD_ENDPOINTS.GET_STATS);
        // The API returns { status, success, data, message }
        if (response.data && response.data.data) {
            return response.data.data as DashboardStats;
        }
        throw new Error('Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch dashboard stats'));
    }
};

// Doctors Patient Count
export interface DoctorPatientCount {
    doctor_id: number;
    doctor_name: string;
    patient_count: number;
}

export const getDoctorsPatientCount = async (): Promise<DoctorPatientCount[]> => {
    try {
        const response = await apiClient.get(DASHBOARD_ENDPOINTS.DOCTORS_PATIENT_COUNT);
        if (response.data && response.data.data && response.data.data.doctors) {
            return response.data.data.doctors.map((item: any) => ({
                doctor_id: item.doctor_id,
                doctor_name: item.doctor_name,
                patient_count: Number(item.patient_count),
            }));
        }
        throw new Error("Unexpected response structure");
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, "Could not fetch doctors patient count"));
    }
};
