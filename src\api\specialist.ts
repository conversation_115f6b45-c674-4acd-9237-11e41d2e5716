import apiClient from '@/src/utils/apiClient';
import { SPECIALIST_ENDPOINTS, ROLES_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray } from '@/src/utils/apiErrorHandler';

export interface Specialist {
    id: number;
    user_uuid?: string;
    specialist_id?: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    specialist_addresses?: string;
    is_active?: boolean;
    is_deleted?: boolean;
    is_verified?: boolean;
    role?: string;
    created_at?: string;
    updated_at?: string;
}

export interface CreateSpecialistInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    specialist_addresses?: string;
    role_id: number;
}

export interface Role {
    id: number;
    role_name: string;
    created_at: string;
    updated_at: string;
}

export const getRoles = async (): Promise<Role[]> => {
    try {
        const response = await apiClient.get(ROLES_ENDPOINTS.GET_ROLES);
        return response.data.data as Role[];
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch roles'));
    }
};
// List specialists
export const getAllSpecialists = async (params: { page: number; limit: number; filter?: 'all' | 'active' | 'inactive' | 'deleted'; search?: string }) => {
    try {
        const query = new URLSearchParams();
        query.append('page', String(params.page));
        query.append('limit', String(params.limit));
        if (params.filter) query.append('filter', params.filter);
        if (params.search && params.search.trim()) query.append('search', params.search.trim());
        const url = `${SPECIALIST_ENDPOINTS.GET_SPECIALISTS}?${query.toString()}`;
        const response = await apiClient.get(url);
        const raw = response.data?.data || {};
        return {
            specialists: raw.specialists || [],
            page: raw.page || 1,
            limit: raw.limit || params.limit,
            totalCount: raw.totalCount || 0,
            totalPages: raw.totalPages || 1,
            filter: raw.filter || params.filter || 'all',
        };
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load specialists'));
    }
};

// Create specialist
export const createSpecialist = async (specialistData: CreateSpecialistInput): Promise<Specialist> => {
    try {
        const response = await apiClient.post(SPECIALIST_ENDPOINTS.CREATE_SPECIALIST, specialistData);
        return extractApiResponseData<Specialist>(response, 'No data returned after creation');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create specialist'));
    }
};

// Update specialist
export const updateSpecialist = async (specialistId: number, specialistData: Partial<CreateSpecialistInput>): Promise<Specialist> => {
    try {
        const response = await apiClient.patch(SPECIALIST_ENDPOINTS.UPDATE_SPECIALIST(specialistId.toString()), specialistData);
        return extractApiResponseData<Specialist>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update specialist'));
    }
};

// View one specialist
export const viewSpecialist = async (specialistId: number): Promise<Specialist> => {
    try {
        const response = await apiClient.get(SPECIALIST_ENDPOINTS.GET_SPECIALIST(specialistId.toString()));
        return extractApiResponseData<Specialist>(response, 'No specialist data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch specialist'));
    }
};

// Delete specialist
export const deleteSpecialist = async (specialistId: number): Promise<void> => {
    try {
        await apiClient.delete(SPECIALIST_ENDPOINTS.DELETE_SPECIALIST(specialistId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete specialist'));
    }
};
