import apiClient from '@/src/utils/apiClient';
import { SPECIALIST_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData } from '@/src/utils/apiErrorHandler';
import { Specialist } from './specialist';

// Toggle the active status of a specialist (is_active field)
export const toggleSpecialistStatus = async (specialistId: number, isActive: boolean): Promise<Specialist> => {
    try {
        const formData = new FormData();
        formData.append('status', isActive ? 'activate' : 'deactivate');
        const response = await apiClient.patch(`/admin/${specialistId}/user-status`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        if (response.data && response.status === 200) {
            return {
                id: specialistId,
                is_active: isActive,
                ...response.data.data || {}
            } as Specialist;
        }
        throw new Error('Invalid response from server');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not toggle specialist status'));
    }
};

// Restore specialist (if soft delete is implemented)
export const restoreSpecialist = async (specialistId: number): Promise<Specialist> => {
    try {
        const response = await apiClient.put(`/user/${specialistId}/restore`);
        return extractApiResponseData<Specialist>(response, 'No data returned after restoration');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not restore specialist'));
    }
};
