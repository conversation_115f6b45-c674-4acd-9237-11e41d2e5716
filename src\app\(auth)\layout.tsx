import React from 'react';
import { Toaster } from 'react-hot-toast';

const AuthLayout = ({ children }: { children: React.ReactNode }) => {
    return (
        <div className="min-h-screen text-black dark:text-white-dark">
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    className: '',
                    style: {
                        background: '#ffffff',
                        color: '#333333',
                        padding: '14px 20px',
                        borderRadius: '10px',
                        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.05)',
                        border: '1px solid #e0e0e0',
                        fontSize: '15px',
                    },
                    success: {
                        iconTheme: {
                            primary: '#4ade80',
                            secondary: '#e0fce4',
                        },
                        style: {
                            background: '#f0fdf4',
                            border: '1px solid #bbf7d0',
                            color: '#166534',
                        },
                    },
                    error: {
                        iconTheme: {
                            primary: '#f87171',
                            secondary: '#fef2f2',
                        },
                        style: {
                            background: '#fef2f2',
                            border: '1px solid #fecaca',
                            color: '#991b1b',
                        },
                    },
                }}
            />
            {children}
        </div>
    );
};

export default AuthLayout;
