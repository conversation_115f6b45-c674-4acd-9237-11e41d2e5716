'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/src/contexts/AuthContext';
import ProviderComponent from '@/src/components/layouts/provider-component';
import ContentAnimation from '@/src/components/layouts/content-animation';
import Footer from '@/src/components/layouts/footer';
import Header from '@/src/components/layouts/header';
import Loading from '@/src/components/layouts/loading';
import MainContainer from '@/src/components/layouts/main-container';
import Overlay from '@/src/components/layouts/overlay';
import ScrollToTop from '@/src/components/layouts/scroll-to-top';
import Sidebar from '@/src/components/layouts/sidebar';
import { Toaster } from 'react-hot-toast'; // ✅ Added import
import '@/src/app/globals.css'; // Import global styles
export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();
    const [checking, setchecking] = useState(true);

    useEffect(() => {
        if (!isAuthenticated && !isLoading) {
            router.push('/login');
            return;
        }
        setchecking(false);
    }, [isAuthenticated, isLoading, router]);

    if (checking) return <Loading />;

    return (
        <>
            <div className="relative">
                <Overlay />
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 4000,
                        className: '',
                        style: {
                            background: '#ffffff',
                            color: '#333333',
                            padding: '14px 20px',
                            borderRadius: '10px',
                            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.05)',
                            border: '1px solid #e0e0e0',
                            fontSize: '15px',
                        },
                        success: {
                            iconTheme: {
                                primary: '#4ade80',
                                secondary: '#e0fce4',
                            },
                            style: {
                                background: '#f0fdf4',
                                border: '1px solid #bbf7d0',
                                color: '#166534',
                            },
                        },
                        error: {
                            iconTheme: {
                                primary: '#f87171',
                                secondary: '#fef2f2',
                            },
                            style: {
                                background: '#fef2f2',
                                border: '1px solid #fecaca',
                                color: '#991b1b',
                            },
                        },
                    }}
                />
                {/* ✅ Added Toaster */}
                <ScrollToTop />

                <MainContainer>
                    <Sidebar />
                    <div className="main-content flex min-h-screen flex-col">
                        <Header />
                        <ContentAnimation>{children}</ContentAnimation>
                        <Footer />
                    </div>
                </MainContainer>
            </div>
        </>
    );
}
