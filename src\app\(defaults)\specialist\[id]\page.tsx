'use client';

import SpecialistModule from '@/src/components/pages/specialist/SpecialistModule';

interface SpecialistPageProps {
    params: {
        id: string;
    };
    searchParams: {
        mode?: 'edit' | 'view' | 'add';
    };
}

const SpecialistPage = ({ params, searchParams }: SpecialistPageProps) => {
    const { id } = params;
    const mode = searchParams.mode;

    if (!id && mode !== 'add') {
        return <p>No specialist ID provided.</p>;
    }

    return <SpecialistModule mode={mode} id={id} />;
};

export default SpecialistPage;
