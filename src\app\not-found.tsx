import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

export const metadata: Metadata = {
    title: 'Error 404',
};

const NotFound = () => {
    return (
        <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
            <div className="">
                <div className="relative">
                    <Image src="/assets/images/error/404-dark.svg" alt="404" className="dark-img mx-auto -mt-10 w-full max-w-xs object-cover md:-mt-14 md:max-w-xl" width={400} height={400} priority />
                    <Image
                        src="/assets/images/error/404-light.gif"
                        alt="404"
                        className="light-img mx-auto -mt-10 w-full max-w-xs object-cover md:-mt-14 md:max-w-xl"
                        width={600}
                        height={400}
                        priority
                    />
                    <p className="mt-5 text-base dark:text-white text-center">The page you requested was not found!</p>
                    <Link href="/" className="btn mx-auto !mt-7 w-max border-0 uppercase shadow-none bg-[#f37023] text-white hover:bg-[#fee2e2] hover:text-[#f37023]">
                        Home
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default NotFound;
