# EmptyState Component Documentation

The `EmptyState` component provides a consistent way to display "no data" messages across your application. It's designed to be flexible and reusable for different contexts.

## Features

- ✅ Consistent styling across the application
- ✅ Support for custom icons
- ✅ Optional action buttons
- ✅ Table and standalone usage modes
- ✅ Customizable messages based on context (search vs empty data)

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `title` | string | Yes | Main heading for the empty state |
| `description` | string | Yes | Detailed description text |
| `icon` | React.ReactNode | No | Custom icon component |
| `action` | object | No | Action button with `label` and `onClick` |
| `colSpan` | number | No | Number of table columns to span (default: 1) |
| `className` | string | No | Additional CSS classes |

## Usage Examples

### 1. Basic Table Usage (like in PlansList)

```tsx
<EmptyState
    colSpan={4}
    title="No plans found"
    description="No plans match your search criteria."
/>
```

### 2. With Action Button

```tsx
<EmptyState
    colSpan={4}
    title="No plans available"
    description="No plans have been created yet. Create your first plan to get started."
    action={{
        label: 'Create First Plan',
        onClick: () => router.push('/plans/add')
    }}
/>
```

### 3. With Custom Icon

```tsx
import { UserGroupIcon } from '@heroicons/react/24/outline';

<EmptyState
    colSpan={3}
    title="No users found"
    description="No users match your search criteria."
    icon={<UserGroupIcon />}
/>
```

### 4. Standalone Usage (outside tables)

```tsx
<EmptyState
    title="No notifications"
    description="You're all caught up! No new notifications."
    className="min-h-[300px]"
/>
```

### 5. Different Icons for Different Contexts

```tsx
// For search results
const searchIcon = (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
);

// For empty data
const documentIcon = (
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
);

<EmptyState
    title={isSearching ? "No results found" : "No data available"}
    description={isSearching ? "Try different search terms" : "Add your first item"}
    icon={isSearching ? searchIcon : documentIcon}
/>
```

## Implementation in Other Tables

You can easily use this component in other table components like `DoctorsList`, `PatientsList`, etc.:

```tsx
// In DoctorsList.tsx
{loading ? (
    <TableSkeleton rows={itemsPerPage} colWidths={TABLE_COL_WIDTHS} />
) : (
    <tbody>
        {doctors.length === 0 ? (
            <EmptyState
                colSpan={5} // Adjust based on your table columns
                title={searchQuery ? 'No doctors found' : 'No doctors available'}
                description={
                    searchQuery 
                        ? `No doctors match "${searchQuery}". Try different search terms.`
                        : 'No doctors have been added yet. Add your first doctor to get started.'
                }
                action={!searchQuery ? {
                    label: 'Add First Doctor',
                    onClick: () => router.push('/doctors/add')
                } : undefined}
            />
        ) : (
            doctors.map((doctor, idx) => (
                // Your table rows here
            ))
        )}
    </tbody>
)}
```

## Styling Notes

- The component uses consistent colors that match your application theme
- The action button uses your brand color (`#eb6309`)
- Icons are displayed in a muted gray color for subtle visual hierarchy
- The component is responsive and works well on different screen sizes

## Accessibility

- Uses semantic HTML with proper heading structure
- Action buttons have appropriate focus states
- Text has sufficient color contrast ratios
- Screen readers can properly navigate the empty state content
