import React from 'react';

interface EmptyStateProps {
    /** Title for the empty state */
    title: string;
    /** Description text to show below the title */
    description: string;
    /** Icon to display (optional) */
    icon?: React.ReactNode;
    /** Action button (optional) */
    action?: {
        label: string;
        onClick: () => void;
    };
    /** Number of columns to span (for table usage) */
    colSpan?: number;
    /** Custom CSS classes */
    className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
    title,
    description,
    icon,
    action,
    colSpan = 1,
    className = '',
}) => {
    const content = (
        <div className={`flex flex-col items-center justify-center py-12 px-4 ${className}`}>
            {/* Icon */}
            {icon && (
                <div className="w-16 h-16 mb-4 text-gray-300">
                    {icon}
                </div>
            )}

            {/* Default icon if none provided */}
            {!icon && (
                <div className="w-16 h-16 mb-4 text-gray-300">
                    <svg
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        className="w-full h-full"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                    </svg>
                </div>
            )}

            {/* Title */}
            <h3 className="text-lg font-medium text-gray-900 mb-2">
                {title}
            </h3>

            {/* Description */}
            <p className="text-gray-500 text-sm max-w-sm text-center mb-4">
                {description}
            </p>

            {/* Action button */}
            {action && (
                <button
                    onClick={action.onClick}
                    className="bg-[#eb6309] text-white px-4 py-2 rounded-lg hover:bg-[#d55708] transition-colors duration-200"
                >
                    {action.label}
                </button>
            )}
        </div>
    );

    // For table usage
    if (colSpan > 1) {
        return (
            <tr>
                <td colSpan={colSpan} className="text-center">
                    {content}
                </td>
            </tr>
        );
    }

    // For standalone usage
    return content;
};

export default EmptyState;
