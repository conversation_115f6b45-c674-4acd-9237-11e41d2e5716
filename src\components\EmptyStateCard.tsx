import React from 'react';

interface EmptyStateCardProps {
    /** Title for the empty state */
    title: string;
    /** Description text to show below the title */
    description: string;
    /** Icon to display (optional) */
    icon?: React.ReactNode;
    /** Action button (optional) */
    action?: {
        label: string;
        onClick: () => void;
    };
    /** Custom CSS classes */
    className?: string;
    /** Minimum height for the container */
    minHeight?: string;
}

const EmptyStateCard: React.FC<EmptyStateCardProps> = ({
    title,
    description,
    icon,
    action,
    className = '',
    minHeight = '400px',
}) => {
    return (
        <div
            className={`bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col items-center justify-center p-8 ${className}`}
            style={{ minHeight }}
        >
            {/* Icon */}
            {icon && (
                <div className="w-20 h-20 mb-6 text-gray-300">
                    {icon}
                </div>
            )}

            {/* Default icon if none provided */}
            {!icon && (
                <div className="w-20 h-20 mb-6 text-gray-300">
                    <svg
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        className="w-full h-full"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                    </svg>
                </div>
            )}

            {/* Title */}
            <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                {title}
            </h3>

            {/* Description */}
            <p className="text-gray-500 text-center max-w-md mb-6 leading-relaxed">
                {description}
            </p>

            {/* Action button */}
            {action && (
                <button
                    onClick={action.onClick}
                    className="bg-[#eb6309] text-white px-6 py-3 rounded-lg hover:bg-[#d55708] transition-colors duration-200 font-medium"
                >
                    {action.label}
                </button>
            )}
        </div>
    );
};

export default EmptyStateCard;
