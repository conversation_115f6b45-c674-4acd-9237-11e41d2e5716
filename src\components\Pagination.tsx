import React, { useState, useRef, useEffect } from 'react';

interface PaginationProps {
    currentPage: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
    onItemsPerPageChange: (itemsPerPage: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({ currentPage, totalItems, itemsPerPage, onPageChange, onItemsPerPageChange }) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            onPageChange(page);
        }
    };

    // Custom dropdown state for rows per page
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const rowsOptions = [10, 20, 50, 100];

    // Calculate dropdown position based on available space
    const calculateDropdownPosition = () => {
        if (dropdownRef.current) {
            const rect = dropdownRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const dropdownHeight = rowsOptions.length * 32 + 16; // Approximate height of dropdown

            // Check if there's enough space below
            const spaceBelow = viewportHeight - rect.bottom;
            const spaceAbove = rect.top;

            if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
                setDropdownPosition('top');
            } else {
                setDropdownPosition('bottom');
            }
        }
    };

    // Close dropdown on outside click and calculate position
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        }

        function handleScroll() {
            if (dropdownOpen) {
                calculateDropdownPosition();
            }
        }

        if (dropdownOpen) {
            calculateDropdownPosition();
            document.addEventListener('mousedown', handleClickOutside);
            window.addEventListener('scroll', handleScroll, true);
            window.addEventListener('resize', handleScroll);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        };
    }, [dropdownOpen]);

    return (
        <div className="flex items-center justify-between my-3 mx-2">
            {/* Left Section: Rows per page and entries details */}
            <div className="flex items-start space-x-4">
                {/* Rows Per Page Dropdown */}
                <div className="flex items-center space-x-2 space-y-1">
                    <span className="text-sm text-gray-600">Rows per page:</span>
                    <div ref={dropdownRef} className="relative min-h-[32px]">
                        <button
                            type="button"
                            className="border border-gray-300 rounded px-2 py-1 text-sm min-w-[60px] bg-white text-black flex items-center justify-between cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#eb6309]"
                            onClick={() => setDropdownOpen((open) => !open)}
                        >
                            {itemsPerPage}
                            <span className="ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </span>
                        </button>
                        {dropdownOpen && (
                            <div
                                className={`absolute left-0 min-w-full z-20 bg-white border rounded shadow-lg max-h-48 overflow-y-auto ${
                                    dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
                                }`}
                            >
                                {rowsOptions.map((option) => (
                                    <div
                                        key={option}
                                        className={`px-4 py-2 cursor-pointer ${itemsPerPage === option ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'} hover:bg-[#eb6309] hover:text-white`}
                                        onClick={() => {
                                            onItemsPerPageChange(option);
                                            setDropdownOpen(false);
                                        }}
                                    >
                                        {option}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                {/* Display Range */}
                <div className="text-sm  text-gray-600 m-2 -space-y-1">
                    Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} entries
                </div>
            </div>

            {/* Right Section: Pagination Controls */}
            <div className="flex items-center space-x-1">
                {/* First Page Button */}
                <button
                    className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${currentPage === 1 ? 'text-gray-400' : 'text-[#EB6309] bg-[#FDE8D4]'} bg-[#FDE8D4]`}
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                >
                    «
                </button>

                {/* Previous Page Button */}
                <button
                    className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${currentPage === 1 ? 'text-gray-400' : 'text-[#EB6309] bg-[#FDE8D4]'} bg-[#FDE8D4]`}
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    ‹
                </button>

                {/* Page Numbers - with ellipsis for many pages */}
                {(() => {
                    // Logic to display page numbers with ellipsis
                    const pageNumbers = [];
                    const maxVisiblePages = 5; // Maximum number of page buttons to show

                    if (totalPages <= maxVisiblePages) {
                        // If we have fewer pages than the max, show all pages
                        for (let i = 1; i <= totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        // Always show first page
                        pageNumbers.push(1);

                        // Calculate start and end of visible page range
                        let startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
                        let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

                        // Adjust if we're near the end
                        if (endPage === totalPages - 1) {
                            startPage = Math.max(2, endPage - (maxVisiblePages - 3));
                        }

                        // Add ellipsis if needed before the range
                        if (startPage > 2) {
                            pageNumbers.push('...');
                        }

                        // Add the visible page range
                        for (let i = startPage; i <= endPage; i++) {
                            pageNumbers.push(i);
                        }

                        // Add ellipsis if needed after the range
                        if (endPage < totalPages - 1) {
                            pageNumbers.push('...');
                        }

                        // Always show last page
                        pageNumbers.push(totalPages);
                    }

                    return pageNumbers.map((page, index) => {
                        if (page === '...') {
                            return (
                                <span key={`ellipsis-${index}`} className="px-2 text-gray-500">
                                    ...
                                </span>
                            );
                        }

                        return (
                            <button
                                key={`page-${page}`}
                                className={`w-8 h-8 cursor-pointer text-center rounded-full text-sm ${currentPage === page ? 'bg-[#EB6309] text-white' : 'text-[#EB6309] bg-[#FDE8D4]'}`}
                                onClick={() => handlePageChange(page as number)}
                            >
                                {page}
                            </button>
                        );
                    });
                })()}

                {/* Next Page Button */}
                <button
                    className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${currentPage === totalPages ? 'text-gray-400' : 'text-[#EB6309] bg-[#FDE8D4]'} bg-[#FDE8D4]`}
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    ›
                </button>

                {/* Last Page Button */}
                <button
                    className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${currentPage === totalPages ? 'text-gray-400' : 'text-[#EB6309] bg-[#FDE8D4]'} bg-[#FDE8D4]`}
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages}
                >
                    »
                </button>
            </div>
        </div>
    );
};

export default Pagination;
