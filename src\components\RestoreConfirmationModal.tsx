import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';

interface RestoreConfirmationModalProps {
  show: boolean;
  onClose: () => void;
  onRestore: () => void;
  title?: string;
  message?: string;
  restoreLabel?: string;
}

const RestoreConfirmationModal: React.FC<RestoreConfirmationModalProps> = ({
  show,
  onClose,
  onRestore,
  title = 'Confirm Restoration',
  message = 'Are you sure you want to restore this item?',
  restoreLabel = 'Restore',
}) => (
  <Transition appear show={show} as={Fragment}>
    <Dialog as="div" className="relative z-50" onClose={onClose}>
      <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
        <div className="fixed inset-0 bg-black bg-opacity-50" />
      </Transition.Child>
      <div className="fixed inset-0 overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4 text-center">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
              <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                {title}
              </Dialog.Title>
              <div className="mt-2 text-gray-600 text-sm">{message}</div>
              <div className="mt-4 flex justify-end gap-3">
                <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={onClose}>
                  Cancel
                </button>
                <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700" onClick={onRestore}>
                  {restoreLabel}
                </button>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </div>
    </Dialog>
  </Transition>
);

export default RestoreConfirmationModal;
