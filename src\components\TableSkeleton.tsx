import React from 'react';

interface TableSkeletonProps {
	rows?: number;
	colWidths: string[];
}

const TableSkeleton: React.FC<TableSkeletonProps> = ({ rows = 8, colWidths }) => {
	return (
		<>
			{Array.from({ length: rows }).map((_, idx) => (
				<tr key={idx} className={`animate-pulse ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
					{colWidths.map((width, colIdx) => (
						<td key={colIdx} className={`py-3 px-4 text-sm text-gray-800 ${width}`}>
							<div className="h-4 bg-gray-200 rounded w-full" />
						</td>
					))}
				</tr>
			))}
		</>
	);
};

export default TableSkeleton;
