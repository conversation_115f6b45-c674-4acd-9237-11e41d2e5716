import React from 'react';

interface DashboardIconProps {
    active?: boolean;
    className?: string;
}

const DashboardIcon: React.FC<DashboardIconProps> = ({ active, className }) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 18 19" fill="none" className={className}>
            <path
                d="M6.25 6.5H2.25C1.0095 6.5 0 5.4905 0 4.25V2.75C0 1.5095 1.0095 0.5 2.25 0.5H6.25C7.4905 0.5 8.5 1.5095 8.5 2.75V4.25C8.5 5.4905 7.4905 6.5 6.25 6.5ZM6.25 18.5H2.25C1.0095 18.5 0 17.4905 0 16.25V9.75C0 8.5095 1.0095 7.5 2.25 7.5H6.25C7.4905 7.5 8.5 8.5095 8.5 9.75V16.25C8.5 17.4905 7.4905 18.5 6.25 18.5ZM15.75 18.5H11.75C10.5095 18.5 9.5 17.4905 9.5 16.25V14.75C9.5 13.5095 10.5095 12.5 11.75 12.5H15.75C16.9905 12.5 18 13.5095 18 14.75V16.25C18 17.4905 16.9905 18.5 15.75 18.5ZM15.75 11.5H11.75C10.5095 11.5 9.5 10.4905 9.5 9.25V2.75C9.5 1.5095 10.5095 0.5 11.75 0.5H15.75C16.9905 0.5 18 1.5095 18 2.75V9.25C18 10.4905 16.9905 11.5 15.75 11.5Z"
                fill="#eb6309"
            />
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="" viewBox="0 0 24 25" fill="none" className={className}>
            <path
                d="M5.25 3.5C4.01625 3.5 3 4.51625 3 5.75V7.25C3 8.48375 4.01625 9.5 5.25 9.5H9.25C10.4838 9.5 11.5 8.48375 11.5 7.25V5.75C11.5 4.51625 10.4838 3.5 9.25 3.5H5.25ZM14.75 3.5C13.5162 3.5 12.5 4.51625 12.5 5.75V12.25C12.5 13.4838 13.5162 14.5 14.75 14.5H18.75C19.9838 14.5 21 13.4838 21 12.25V5.75C21 4.51625 19.9838 3.5 18.75 3.5H14.75ZM5.25 5H9.25C9.67325 5 10 5.32675 10 5.75V7.25C10 7.67325 9.67325 8 9.25 8H5.25C4.82675 8 4.5 7.67325 4.5 7.25V5.75C4.5 5.32675 4.82675 5 5.25 5ZM14.75 5H18.75C19.1733 5 19.5 5.32675 19.5 5.75V12.25C19.5 12.6732 19.1733 13 18.75 13H14.75C14.3268 13 14 12.6732 14 12.25V5.75C14 5.32675 14.3268 5 14.75 5ZM5.25 10.5C4.01625 10.5 3 11.5162 3 12.75V19.25C3 20.4838 4.01625 21.5 5.25 21.5H9.25C10.4838 21.5 11.5 20.4838 11.5 19.25V12.75C11.5 11.5162 10.4838 10.5 9.25 10.5H5.25ZM5.25 12H9.25C9.67325 12 10 12.3268 10 12.75V19.25C10 19.6733 9.67325 20 9.25 20H5.25C4.82675 20 4.5 19.6733 4.5 19.25V12.75C4.5 12.3268 4.82675 12 5.25 12ZM14.75 15.5C13.5162 15.5 12.5 16.5162 12.5 17.75V19.25C12.5 20.4838 13.5162 21.5 14.75 21.5H18.75C19.9838 21.5 21 20.4838 21 19.25V17.75C21 16.5162 19.9838 15.5 18.75 15.5H14.75ZM14.75 17H18.75C19.1733 17 19.5 17.3268 19.5 17.75V19.25C19.5 19.6733 19.1733 20 18.75 20H14.75C14.3268 20 14 19.6733 14 19.25V17.75C14 17.3268 14.3268 17 14.75 17Z"
                fill="#eb6309"
                stroke="#f36e20"
                strokeWidth="0.2"
            />
        </svg>
    );

export default DashboardIcon;
