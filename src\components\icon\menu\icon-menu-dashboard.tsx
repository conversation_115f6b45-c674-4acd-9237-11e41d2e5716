import { FC } from 'react';

interface IconMenuDashboardProps {
    className?: string;
}

const IconMenuDashboard: FC<IconMenuDashboardProps> = ({ className }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" className={className}>
            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" fill="#eb6309" />
        </svg>
    );
};

export const IconMenuDashboardActive: FC<IconMenuDashboardProps> = ({ className }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" className={className}>
            <rect x="1" y="1" width="22" height="22" rx="5" fill="#fff3e6" stroke="#eb6309" strokeWidth="2" />
            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" fill="#eb6309" />
        </svg>
    );
};

export default IconMenuDashboard;
