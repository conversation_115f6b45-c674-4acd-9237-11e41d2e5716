import React from 'react';

interface DoctorIconProps {
    active?: boolean;
    className?: string;
}

const DoctorIcon: React.FC<DoctorIconProps> = ({ active, className }) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" width="24" height="24" className={className}>
            <path
                d="M25 0C17.296875 0 15.898438 2.171875 15.65625 2.84375C15.628906 2.917969 15.601563 3.015625 15.59375 3.09375L14.4375 13.28125C15.96875 12.613281 18.066406 12.699219 20.78125 12.84375C22.042969 12.910156 23.472656 13 25 13C26.523438 13 27.957031 12.910156 29.21875 12.84375C31.929688 12.699219 34.03125 12.613281 35.5625 13.28125L34.40625 3.09375C34.398438 3.015625 34.371094 2.917969 34.34375 2.84375C34.101563 2.171875 32.699219 0 25 0 Z M 24 4L26 4L26 7L29 7L29 9L26 9L26 12L24 12L24 9L21 9L21 7L24 7 Z M 18.65625 14.75C16.78125 14.695313 15.34375 14.816406 14.65625 15.46875C14.207031 15.898438 14 16.664063 14 17.8125C14 23.878906 18.933594 28.8125 25 28.8125C31.066406 28.8125 36 23.878906 36 17.8125C36 16.664063 35.792969 15.898438 35.34375 15.46875C34.429688 14.601563 32.160156 14.695313 29.3125 14.84375C28.027344 14.910156 26.578125 15 25 15C23.421875 15 21.972656 14.910156 20.6875 14.84375C19.976563 14.804688 19.28125 14.769531 18.65625 14.75 Z M 25 29.8125C22.757813 29.8125 20.527344 30.015625 18.40625 30.40625L25 46.40625L31.6875 30.4375C29.53125 30.03125 27.28125 29.8125 25 29.8125 Z M 16.4375 30.84375C8.300781 32.820313 2 37.347656 2 42.09375L2 50L25 50C24.597656 50 24.214844 49.75 24.0625 49.375 Z M 25 50L48 50L48 42.09375C48 37.371094 41.769531 32.835938 33.6875 30.84375L25.9375 49.375C25.78125 49.746094 25.402344 50 25 50Z"
                fill="#eb6309"
            />
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" width="24" height="24" className={className}>
            <path
                d="M25 0C21.101563 0 18.875 0.546875 17.53125 1.1875C16.859375 1.507813 16.410156 1.851563 16.125 2.15625C15.839844 2.460938 15.65625 2.84375 15.65625 2.84375C15.625 2.925781 15.605469 3.007813 15.59375 3.09375L14 16.875C13.984375 17.011719 13.992188 17.148438 14.03125 17.28125C14.027344 17.457031 14 17.625 14 17.8125C14 23.859375 18.953125 28.8125 25 28.8125C31.046875 28.8125 36 23.859375 36 17.8125C36 17.636719 35.972656 17.480469 35.96875 17.3125C36.011719 17.171875 36.023438 17.019531 36 16.875L34.40625 3.09375C34.394531 3.007813 34.375 2.925781 34.34375 2.84375C34.34375 2.84375 34.160156 2.460938 33.875 2.15625C33.589844 1.851563 33.140625 1.507813 32.46875 1.1875C31.125 0.546875 28.898438 0 25 0 Z M 25 2C28.703125 2 30.667969 2.542969 31.625 3C32.101563 3.230469 32.335938 3.425781 32.4375 3.53125L33.53125 13C33.339844 12.9375 33.136719 12.875 32.9375 12.84375C31.019531 12.523438 28.625 13 25 13C23.1875 13 21.667969 12.890625 20.375 12.8125C19.082031 12.734375 18.019531 12.683594 17.0625 12.84375C16.863281 12.875 16.660156 12.9375 16.46875 13L17.5625 3.53125C17.664063 3.425781 17.898438 3.230469 18.375 3C19.332031 2.542969 21.296875 2 25 2 Z M 24 4L24 7L21 7L21 9L24 9L24 12L26 12L26 9L29 9L29 7L26 7L26 4 Z M 17.90625 14.75C19.285156 14.6875 21.695313 15 25 15C28.777344 15 31.390625 14.605469 32.625 14.8125C33.242188 14.914063 33.421875 15.054688 33.625 15.40625C33.828125 15.757813 34 16.515625 34 17.8125C34 22.765625 29.953125 26.8125 25 26.8125C20.046875 26.8125 16 22.765625 16 17.8125C16 16.515625 16.171875 15.757813 16.375 15.40625C16.578125 15.054688 16.757813 14.914063 17.375 14.8125C17.527344 14.785156 17.710938 14.757813 17.90625 14.75 Z M 25 29.8125C18.785156 29.8125 13.15625 31.339844 9 33.59375C4.84375 35.847656 2 38.820313 2 42.09375L2 50L24.78125 50C24.914063 50.027344 25.054688 50.027344 25.1875 50L48 50L48 42.09375C48 38.820313 45.15625 35.847656 41 33.59375C38.667969 32.328125 35.855469 31.289063 32.75 30.625C32.578125 30.527344 32.382813 30.484375 32.1875 30.5C29.921875 30.050781 27.515625 29.8125 25 29.8125 Z M 25 31.8125C27.023438 31.8125 28.984375 31.96875 30.84375 32.28125L25 46.375L19.15625 32.28125C21.015625 31.96875 22.976563 31.8125 25 31.8125 Z M 17.1875 32.71875L23.5 48L4 48L4 42.09375C4 40.066406 6.117188 37.414063 9.9375 35.34375C11.96875 34.242188 14.441406 33.347656 17.1875 32.71875 Z M 32.8125 32.71875C35.558594 33.347656 38.03125 34.242188 40.0625 35.34375C43.882813 37.414063 46 40.066406 46 42.09375L46 48L26.5 48Z"
                fill="#eb6309"
                stroke="#f36e20"
                strokeWidth="1"
            />
        </svg>
    );

export default DoctorIcon;
