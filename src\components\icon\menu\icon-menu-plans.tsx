import React from 'react';

interface PlansIconProps {
    active?: boolean;
    className?: string;
}

const PlansIcon: React.FC<PlansIconProps> = ({ active, className }) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24" height="24" className={className}>
            <path
                d="M12.5 4C10.019 4 8 6.019 8 8.5L8 39.5C8 41.981 10.019 44 12.5 44L35.5 44C37.981 44 40 41.981 40 39.5L40 20L28.5 20C26.019 20 24 17.981 24 15.5L24 4L12.5 4 z M 27 4.8789062L27 15.5C27 16.327 27.673 17 28.5 17L39.121094 17L27 4.8789062 z M 15.5 24L32.5 24C33.328 24 34 24.671 34 25.5C34 26.329 33.328 27 32.5 27L15.5 27C14.672 27 14 26.329 14 25.5C14 24.671 14.672 24 15.5 24 z M 15.5 30L24.5 30C25.328 30 26 30.671 26 31.5C26 32.329 25.328 33 24.5 33L15.5 33C14.672 33 14 32.329 14 31.5C14 30.671 14.672 30 15.5 30 z M 30.5 30L32.5 30C33.328 30 34 30.671 34 31.5C34 32.329 33.328 33 32.5 33L30.5 33C29.672 33 29 32.329 29 31.5C29 30.671 29.672 30 30.5 30 z M 15.5 36L24.5 36C25.328 36 26 36.671 26 37.5C26 38.329 25.328 39 24.5 39L15.5 39C14.672 39 14 38.329 14 37.5C14 36.671 14.672 36 15.5 36 z M 30.5 36L32.5 36C33.328 36 34 36.671 34 37.5C34 38.329 33.328 39 32.5 39L30.5 39C29.672 39 29 38.329 29 37.5C29 36.671 29.672 36 30.5 36 z"
                fill="#EB6309"
            />
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24" height="24" className={className}>
            <path
                d="M12.5 4C10.032499 4 8 6.0324991 8 8.5L8 39.5C8 41.967501 10.032499 44 12.5 44L35.5 44C37.967501 44 40 41.967501 40 39.5L40 18.5 A 1.50015 1.50015 0 0 0 39.560547 17.439453L39.544922 17.423828L26.560547 4.4394531 A 1.50015 1.50015 0 0 0 25.5 4L12.5 4 z M 12.5 7L24 7L24 15.5C24 17.967501 26.032499 20 28.5 20L37 20L37 39.5C37 40.346499 36.346499 41 35.5 41L12.5 41C11.653501 41 11 40.346499 11 39.5L11 8.5C11 7.6535009 11.653501 7 12.5 7 z M 27 9.1210938L34.878906 17L28.5 17C27.653501 17 27 16.346499 27 15.5L27 9.1210938 z M 15.5 23 A 1.50015 1.50015 0 1 0 15.5 26L32.5 26 A 1.50015 1.50015 0 1 0 32.5 23L15.5 23 z M 15.5 29 A 1.50015 1.50015 0 1 0 15.5 32L24.5 32 A 1.50015 1.50015 0 1 0 24.5 29L15.5 29 z M 30.5 29 A 1.50015 1.50015 0 1 0 30.5 32L32.5 32 A 1.50015 1.50015 0 1 0 32.5 29L30.5 29 z M 15.5 35 A 1.50015 1.50015 0 1 0 15.5 38L24.5 38 A 1.50015 1.50015 0 1 0 24.5 35L15.5 35 z M 30.5 35 A 1.50015 1.50015 0 1 0 30.5 38L32.5 38 A 1.50015 1.50015 0 1 0 32.5 35L30.5 35 z"
                fill="#EB6309"
            />
        </svg>
    );

export default PlansIcon;
