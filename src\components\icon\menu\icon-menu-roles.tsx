import { FC } from 'react';

interface IconMenuRolesProps {
    className?: string;
}

const IconMenuRoles: FC<IconMenuRolesProps> = ({ className }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4l2 4 4-3-2 6H8l-2-6 4 3 2-4z" />
            <circle cx="12" cy="13" r="3" />
            <path d="M8 21v-2a4 4 0 014-4 4 4 0 014 4v2" />
        </svg>
    );
};

export default IconMenuRoles;
