import React from 'react';

interface SpecialistIconProps {
    active?: boolean;
    className?: string;
}

const SpecialistIcon: React.FC<SpecialistIconProps> = ({ active, className }) =>
    active ? (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24" height="24" className={className}>
            <path
                d="M12.5 2C10.57 2 9 3.57 9 5.5L9 20L9 20.5L9 24.257812C7.8597735 25.168256 7 26.430675 7 28C7 30.361739 8.7044726 32.27003 10.919922 32.78125C11.816449 36.336333 13.30684 39.503248 15.40625 41.828125C17.708437 44.377556 20.706886 46 24 46C27.293114 46 30.291563 44.377556 32.59375 41.828125C34.69316 39.503248 36.183551 36.336333 37.080078 32.78125C39.295527 32.27003 41 30.361739 41 28C41 26.430284 40.137097 25.174656 39 24.269531L39 20.5L39 20L39 5.5C39 3.57 37.43 2 35.5 2L12.5 2 z M 23.5 8C24.328 8 25 8.672 25 9.5L25 11L26.5 11C27.328 11 28 11.672 28 12.5C28 13.328 27.328 14 26.5 14L25 14L25 15.5C25 16.328 24.328 17 23.5 17C22.672 17 22 16.328 22 15.5L22 14L20.5 14C19.672 14 19 13.328 19 12.5C19 11.672 19.672 11 20.5 11L22 11L22 9.5C22 8.672 22.672 8 23.5 8 z M 12 22L36 22L36 24.878906 A 1.50015 1.50015 0 0 0 36.855469 26.234375C37.54264 26.561015 38 27.202806 38 28C38 29.122343 37.122343 30 36 30C36.264714 30 36.24617 29.994141 35.939453 29.994141 A 1.50015 1.50015 0 0 0 34.470703 31.185547C33.727184 34.728666 32.236501 37.746337 30.367188 39.816406C28.497877 41.886516 26.317886 43 24 43C21.682114 43 19.502126 41.886475 17.632812 39.816406C15.763499 37.746337 14.272816 34.728666 13.529297 31.185547 A 1.50015 1.50015 0 0 0 12.060547 29.994141C11.753833 29.994141 11.735286 30 12 30C10.877657 30 10 29.122343 10 28C10 27.196375 10.461497 26.541671 11.142578 26.21875 A 1.50015 1.50015 0 0 0 12 24.863281L12 22 z M 19 26 A 2 2 0 0 0 19 30 A 2 2 0 0 0 19 26 z M 29 26 A 2 2 0 0 0 29 30 A 2 2 0 0 0 29 26 z "
                fill="#f36e20"
            />
        </svg>
    ) : (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24" height="24" className={className}>
            <path
                d="M12.5 2C10.585045 2 9 3.5850452 9 5.5L9 20.5L9 24.257812C7.8597735 25.168256 7 26.430675 7 28C7 30.361739 8.7044726 32.27003 10.919922 32.78125C11.816449 36.336333 13.30684 39.503248 15.40625 41.828125C17.708437 44.377556 20.706886 46 24 46C27.293114 46 30.291563 44.377556 32.59375 41.828125C34.69316 39.503248 36.183551 36.336333 37.080078 32.78125C39.295527 32.27003 41 30.361739 41 28C41 26.430284 40.137097 25.174656 39 24.269531L39 20.5L39 5.5C39 3.5850452 37.414955 2 35.5 2L12.5 2 z M 12.5 5L35.5 5C35.795045 5 36 5.2049548 36 5.5L36 19L12 19L12 5.5C12 5.2049548 12.204955 5 12.5 5 z M 23.5 8C22.672 8 22 8.672 22 9.5L22 11L20.5 11C19.672 11 19 11.672 19 12.5C19 13.328 19.672 14 20.5 14L22 14L22 15.5C22 16.328 22.672 17 23.5 17C24.328 17 25 16.328 25 15.5L25 14L26.5 14C27.328 14 28 13.328 28 12.5C28 11.672 27.328 11 26.5 11L25 11L25 9.5C25 8.672 24.328 8 23.5 8 z M 12 22L36 22L36 24.878906 A 1.50015 1.50015 0 0 0 36.855469 26.234375C37.54264 26.561015 38 27.202806 38 28C38 29.122343 37.122343 30 36 30C36.264714 30 36.24617 29.994141 35.939453 29.994141 A 1.50015 1.50015 0 0 0 34.470703 31.185547C33.727184 34.728666 32.236501 37.746337 30.367188 39.816406C28.497877 41.886516 26.317886 43 24 43C21.682114 43 19.502126 41.886475 17.632812 39.816406C15.763499 37.746337 14.272816 34.728666 13.529297 31.185547 A 1.50015 1.50015 0 0 0 12.060547 29.994141C11.753833 29.994141 11.735286 30 12 30C10.877657 30 10 29.122343 10 28C10 27.196375 10.461497 26.541671 11.142578 26.21875 A 1.50015 1.50015 0 0 0 12 24.863281L12 22 z M 19 26 A 2 2 0 0 0 19 30 A 2 2 0 0 0 19 26 z M 29 26 A 2 2 0 0 0 29 30 A 2 2 0 0 0 29 26 z"
                fill="#f36e20"
                stroke="#f36e20"
                strokeWidth="0.0001"
            />
        </svg>
    );

export default SpecialistIcon;
