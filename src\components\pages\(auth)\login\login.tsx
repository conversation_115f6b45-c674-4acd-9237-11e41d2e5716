'use client';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/src/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

const LoginPage = () => {
    const [formData, setFormData] = useState({
        email: '', // Start with empty email
        password: '', // Start with empty password
    });
    const [error, setError] = useState<{ email?: string; password?: string; form?: string }>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [touchedFields, setTouchedFields] = useState<{ email?: boolean; password?: boolean }>({});

    const { login, isAuthenticated, isLoading } = useAuth();
    const router = useRouter();
    const [checking, setchecking] = useState(true);

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated && !isLoading) {
            router.push('/dashboard');
            return;
        }
        setchecking(false);
    }, [isAuthenticated, isLoading, router]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
        // Clear error when user starts typing
        if (error[name as keyof typeof error]) {
            setError((prev) => ({ ...prev, [name]: undefined }));
        }
    };

    // Add email validation for missing '@' symbol
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        setTouchedFields((prev) => ({ ...prev, [name]: true }));

        if (name === 'email') {
            if (!value.includes('@')) {
                setError((prev) => ({ ...prev, email: 'Please enter a valid email address with a complete domain (e.g., <EMAIL>)' }));
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                setError((prev) => ({ ...prev, email: 'Please enter a valid email address with a complete domain (e.g., <EMAIL>)' }));
            } else {
                setError((prev) => ({ ...prev, email: undefined })); // Clear email error if valid
            }
        }
    };

    // Add custom validation logic for email and password fields
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError({}); // Reset errors

        // Custom validation
        const errors: { email?: string; password?: string } = {};
        if (!formData.email) {
            errors.email = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            errors.email = 'Please enter a valid email address with a complete domain (e.g., <EMAIL>)';
        }

        if (!formData.password) {
            errors.password = 'Password is required';
        }

        if (Object.keys(errors).length > 0) {
            setError(errors);
            return;
        }

        setIsSubmitting(true);

        try {
            const response = await login(formData);

            if (!response.success) {
                toast.error(response.message || 'Login failed. Please try again.');
                setError({ form: response.message || 'Login failed. Please try again.' });
            } else {
                toast.success('Login successful!');
                router.push('/dashboard');
            }
        } catch (err) {
            console.error('Login error:', err);
            toast.error('An unexpected error occurred. Please try again.');
            setError({ form: 'An unexpected error occurred. Please try again.' });
        } finally {
            setIsSubmitting(false);
        }
    };

    const backgroundImageStyle = { backgroundImage: "url('/assets/images/BGauth.png')" };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 flex-col bg-cover bg-no-repeat bg-center" style={backgroundImageStyle}>
            {/* Logo Section */}
            <div className="mb-5">
                <Image src="/assets/images/logo.png" alt="Logo" className="mx-auto w-[240px] mb-5" width={240} height={45} priority />
            </div>

            <div className="bg-white p-8 rounded shadow-md w-full max-w-sm text-center">
                {/* Welcome Heading */}
                <h1 className="text-[38px] leading-[45px] font-bold text-gray-700 mb-5">Welcome to Dashboard</h1>

                <h2 className="text-2xl font-semibold mb-6 text-[rgb(235,99,9)]">Login</h2>

                {/* Error Message */}
                {/* {error.form && <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">{error.form}</div>} */}

                <form onSubmit={handleSubmit} className="space-y-4 text-left">
                    <div>
                        <label className="block text-gray-700 mb-1">Email</label>
                        <input
                            type="text" // Changed from "email" to "text"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            onBlur={handleBlur} // Trigger validation on blur
                            placeholder="Enter email"
                            disabled={isSubmitting}
                            className={`w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)] disabled:bg-gray-100 disabled:cursor-not-allowed ${touchedFields.email && error.email && !error.form ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {touchedFields.email && error.email && !error.form && <p className="text-red-500 text-sm mt-1">{error.email}</p>}
                    </div>

                    <div>
                        <label className="block text-gray-700 mb-1">Password</label>
                        <div className="relative">
                            <input
                                type={showPassword ? 'text' : 'password'}
                                name="password"
                                value={formData.password}
                                onChange={handleInputChange}
                                placeholder="Enter password"
                                disabled={isSubmitting}
                                className={`w-full px-4 py-2 ${formData.password ? 'pr-12' : ''} border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)] disabled:bg-gray-100 disabled:cursor-not-allowed ${error.password ? 'border-red-500' : 'border-gray-300'}`}
                            />
                            {formData.password && (
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                                    disabled={isSubmitting}
                                >
                                    {showPassword ? (
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="15" height="20">
                                            <path
                                                d="M1.7070312 0.29296875L0.29296875 1.7070312L28.292969 29.707031L29.707031 28.292969L23.681641 22.267578C27.777456 19.49434 29.799165 15.616636 29.826172 15.564453 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5C12.469857 5 10.199331 5.7501922 8.234375 6.8203125L1.7070312 0.29296875 z M 15 8C18.866 8 22 11.134 22 15C22 16.571956 21.470043 18.012848 20.59375 19.179688L17.701172 16.287109C17.889655 15.897819 18 15.462846 18 15C18 13.343 16.657 12 15 12C14.537154 12 14.102181 12.110345 13.712891 12.298828L10.820312 9.40625C11.987152 8.5299565 13.428044 8 15 8 z M 4.9511719 9.0761719C1.9791583 11.576125 0.27498083 14.287031 0.21875 14.376953 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C16.85 25 18.520531 24.673484 20.019531 24.146484L17.431641 21.556641C16.672641 21.838641 15.856 22 15 22C11.134 22 8 18.866 8 15C8 14.144 8.1613594 13.327359 8.4433594 12.568359L4.9511719 9.0761719 z"
                                                fill="gray"
                                            />
                                        </svg>
                                    ) : (
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="15" height="20">
                                            <path
                                                d="M15 5C6.081703 5 0.32098813 14.21118 0.21679688 14.378906 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C24.938822 25 29.767326 15.678741 29.826172 15.564453 A 1 1 0 0 0 29.837891 15.544922 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.785156 14.380859 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5 z M 15 8C18.866 8 22 11.134 22 15C22 18.866 18.866 22 15 22C11.134 22 8 18.866 8 15C8 11.134 11.134 8 15 8 z M 15 12 A 3 3 0 0 0 12 15 A 3 3 0 0 0 15 18 A 3 3 0 0 0 18 15 A 3 3 0 0 0 15 12 z"
                                                fill="gray"
                                            />
                                        </svg>
                                    )}
                                </button>
                            )}
                        </div>
                        {error.password && <p className="text-red-500 text-sm mt-1">{error.password}</p>}
                    </div>

                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-[rgb(235,99,9)] text-white py-2 rounded hover:bg-orange-700 transition duration-200 disabled:bg-[#fee2e2] disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        {isSubmitting ? 'Logging in...' : 'Login'}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default LoginPage;
