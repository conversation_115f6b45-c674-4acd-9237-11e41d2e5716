'use client';

import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import ApexCharts from 'apexcharts';
import { getDashboardStats, DashboardStats, getDoctorsPatientCount, DoctorPatientCount } from '@/src/api/doctor';
let lastDate = new Date().getTime();
const XAXIS_RANGE = 60000; // 60 seconds

// Utility Functions
const generateDataPoint = () => {
    lastDate += 1000;
    return [lastDate, Math.floor(Math.random() * 80) + 10] as [number, number];
};

const getInitialData = (): [number, number][] => {
    const init: [number, number][] = [];
    for (let i = 0; i < 60; i++) {
        init.push(generateDataPoint());
    }
    return init;
};

const DashboardCharts = () => {
    const [areaSeries] = useState([
        {
            name: 'Appointments',
            data: [12, 18, 20, 25, 22, 30, 28, 35, 40, 38, 42, 45],
        },
        {
            name: 'Treatments',
            data: [5, 8, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33],
        },
    ]);

    const areaOptions: ApexOptions = {
        chart: { height: 350, type: 'area', dropShadow: { enabled: true, top: 3, left: 2, blur: 4, opacity: 0.15 } },
        colors: ['#EB6309', '#1E90FF'],
        dataLabels: { enabled: false },
        stroke: { curve: 'smooth' },
        xaxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
        tooltip: {
            x: { formatter: (val: string | number): string => String(val) },
        },
    };

    // Stats Cards State
    const [statsData, setStatsData] = useState<DashboardStats>({
        doctors: 0,
        patients: 0,
        specialist: 0,
        pendingPatients: 0,
        incompletePatients: 0,
        completePatients: 0,
    });
    const [statsLoading, setStatsLoading] = useState<boolean>(true);
    const [statsError, setStatsError] = useState<string | null>(null);

    // Donut Chart
    const [donutSeries, setDonutSeries] = useState<number[]>([0, 0, 0]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // Fetch dashboard stats for both stats cards and donut chart
        setStatsLoading(true);
        getDashboardStats()
            .then((stats: DashboardStats) => {
                setStatsData(stats);
                setDonutSeries([stats.doctors, stats.specialist, stats.completePatients, stats.incompletePatients, stats.pendingPatients]); // Add pending patients
                setLoading(false);
                setStatsLoading(false);
            })
            .catch((err) => {
                const errorMessage = err.message || 'Failed to load dashboard stats';
                setError(errorMessage);
                setStatsError(errorMessage);
                setLoading(false);
                setStatsLoading(false);
            });
    }, []);

    // Donut Chart click handler
    const donutOptionss: ApexCharts.ApexOptions = {
        chart: {
            type: 'donut',
            events: {
                dataPointMouseEnter: (event, chartContext, config) => {
                    event.target.style.cursor = 'pointer';
                },
                dataPointMouseLeave: (event, chartContext, config) => {
                    event.target.style.cursor = 'default';
                },
                dataPointSelection: (event, chartContext, config) => {
                    const label = config.w.config.labels[config.dataPointIndex]; // slice label

                    if (label === 'Doctors') {
                        window.location.href = '/doctor?filter=all';
                    } else if (label === 'Specialists') {
                        window.location.href = '/specialist';
                    } else if (label === 'Completed Patients') {
                        window.location.href = '/patient?status=completed';
                    } else if (label === 'Incomplete Patients') {
                        window.location.href = '/patient?status=incomplete';
                    } else if (label === 'Pending Patients') {
                        window.location.href = '/patient?status=sent_by_doctor'; // Navigate to pending patients
                    }
                },
            },
        },
        labels: ['Doctors', 'Specialists', 'Completed Patients', 'Incomplete Patients', 'Pending Patients'], // Add label for pending patients
        colors: ['#EB6309', '#1E90FF', '#22C55E', '#FFA500', '#FFD700'], // Add color for pending patients
        legend: { position: 'bottom' },
    };

    // Top Doctors Leaderboard Data
    const [topDoctors, setTopDoctors] = useState<DoctorPatientCount[]>([]);
    const [leaderboardLoading, setLeaderboardLoading] = useState<boolean>(true);
    const [leaderboardError, setLeaderboardError] = useState<string | null>(null);

    useEffect(() => {
        setLeaderboardLoading(true);
        getDoctorsPatientCount()
            .then((data: DoctorPatientCount[]) => {
                // Sort by patient count and take top 10
                const sortedDoctors = data.sort((a, b) => b.patient_count - a.patient_count).slice(0, 10);
                setTopDoctors(sortedDoctors);
                setLeaderboardLoading(false);
            })
            .catch((err) => {
                setLeaderboardError(err.message || 'Failed to load top doctors');
                setLeaderboardLoading(false);
            });
    }, []);

    // Get medal color based on position
    const getMedalIcon = (position: number) => {
        if (position === 1) return '🏆'; // Gold trophy
        if (position === 2) return '🥈'; // Silver medal
        if (position === 3) return '🥉'; // Bronze medal
        return `#${position}`; // Number for others
    };

    const getRankBadgeColor = (position: number) => {
        if (position === 1) return 'bg-yellow-500 text-white';
        if (position === 2) return 'bg-gray-400 text-white';
        if (position === 3) return 'bg-amber-600 text-white';
        return 'bg-blue-500 text-white';
    };

    return (
        <>
            <h2 className="text-2xl font-bold mb-6">Dashboard</h2>

            {/* Stats Cards Section - 5 Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
                {/* Total Doctors Card */}
                <div
                    className="bg-white rounded-2xl p-4 shadow-sm border border-indigo-500 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                        window.location.href = '/doctor?filter=all';
                    }}
                >
                    <div className="flex flex-col items-center text-center">
                        <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center mb-3">
                            <svg className="w-5 h-5 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{statsLoading ? '...' : statsError ? '0' : statsData.doctors}</p>
                        <p className="text-xs text-gray-600 font-medium">Total Doctors</p>
                    </div>
                </div>
                {/* Total Specialists Card */}
                <div
                    className="bg-white rounded-2xl p-4 shadow-sm border border-purple-500 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                        window.location.href = '/specialist';
                    }}
                >
                    <div className="flex flex-col items-center text-center">
                        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mb-3">
                            <svg className="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    fillRule="evenodd"
                                    d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                                    clipRule="evenodd"
                                />
                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                            </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{statsLoading ? '...' : statsError ? '0' : statsData.specialist}</p>
                        <p className="text-xs text-gray-600 font-medium">Total Specialists</p>
                    </div>
                </div>
                {/* Complete Patients Card */}
                <div
                    className="bg-white rounded-2xl p-4 shadow-sm border border-green-500 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                        window.location.href = '/patient?status=completed';
                    }}
                >
                    <div className="flex flex-col items-center text-center">
                        <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mb-3">
                            <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{statsLoading ? '...' : statsError ? '0' : statsData.completePatients}</p>
                        <p className="text-xs text-gray-600 font-medium">Completed Patients</p>
                    </div>
                </div>
                {/* Incomplete Patients Card */}
                <div
                    className="bg-white rounded-2xl p-4 shadow-sm border border-orange-500 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                        window.location.href = '/patient?status=incomplete';
                    }}
                >
                    <div className="flex flex-col items-center text-center">
                        <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center mb-3">
                            <svg className="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{statsLoading ? '...' : statsError ? '0' : statsData.incompletePatients}</p>
                        <p className="text-xs text-gray-600 font-medium">Incomplete Patients</p>
                    </div>
                </div>
                {/* Pending Patients Card */}
                <div
                    className="bg-white rounded-2xl p-4 shadow-sm border border-yellow-500 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                        // Navigate to patients page with pending filter
                        window.location.href = '/patient?status=sent_by_doctor';
                    }}
                >
                    <div className="flex flex-col items-center text-center">
                        <div className="w-10 h-10 bg-yellow-100 rounded-xl flex items-center justify-center mb-3">
                            <svg className="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{statsLoading ? '...' : statsError ? '0' : statsData.pendingPatients}</p>
                        <p className="text-xs text-gray-600 font-medium">Pending Patients</p>
                    </div>
                </div>
            </div>

            <div className="p-6 bg-white rounded shadow-md mx-auto space-y-12">
                {/* Area Chart - Full Width */}

                {/* Donut & Line Chart - Side by Side */}
                <div className="flex flex-col md:flex-row md:space-x-6 space-y-8 md:space-y-0">
                    {/* Donut Chart */}
                    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
                        <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">Doctors, Specialists & Patients</h2>
                        <div className="flex justify-center">
                            {loading ? (
                                <div className="text-center text-gray-500">Loading...</div>
                            ) : error ? (
                                <div className="text-center text-red-500">{error}</div>
                            ) : (
                                <Chart options={donutOptionss} series={donutSeries} type="donut" height={320} />
                            )}
                        </div>
                    </div>

                    {/* Top Doctors Leaderboard */}
                    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
                        <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">🏆 Top Doctors Leaderboard</h2>
                        {leaderboardLoading ? (
                            <div className="text-center text-gray-500">Loading...</div>
                        ) : leaderboardError ? (
                            <div className="text-center text-red-500">{leaderboardError}</div>
                        ) : (
                            <div className="space-y-3 max-h-80 overflow-y-auto">
                                {topDoctors.map((doctor, index) => (
                                    <div
                                        key={doctor.doctor_name}
                                        className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 hover:shadow-md ${
                                            index < 3 ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                                        }`}
                                    >
                                        <div className="flex items-center space-x-3">
                                            {/* Rank Badge */}
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getRankBadgeColor(index + 1)}`}>
                                                {index < 3 ? getMedalIcon(index + 1) : index + 1}
                                            </div>

                                            {/* Doctor Info */}
                                            <div>
                                                <p className={`font-semibold ${index < 3 ? 'text-gray-800' : 'text-gray-700'}`}>{doctor.doctor_name}</p>
                                                <p className="text-xs text-gray-500">
                                                    {index === 0 ? '👑 Top Performer' : index === 1 ? '🌟 Excellent' : index === 2 ? '💪 Great Work' : 'Active Doctor'}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Patient Count */}
                                        <div className="text-right">
                                            <p className={`text-lg font-bold ${index < 3 ? 'text-blue-600' : 'text-gray-600'}`}>{doctor.patient_count}</p>
                                            <p className="text-xs text-gray-500">patients</p>
                                        </div>
                                    </div>
                                ))}

                                {topDoctors.length === 0 && <div className="text-center text-gray-500 py-8">No doctors data available</div>}
                            </div>
                        )}
                    </div>
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">Monthly Appointments & Treatments</h2>
                    <Chart options={areaOptions} series={areaSeries} type="area" height={350} />
                </div>
            </div>
        </>
    );
};

export default DashboardCharts;
