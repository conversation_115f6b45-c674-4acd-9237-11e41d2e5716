'use client';

import React, { useEffect, useState } from 'react';
import { viewPatient, PatientDetails } from '@/src/api/patient';
import Loading from '@/src/components/layouts/loading';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { FileText, FolderOpen, User, CalendarDays, ShieldCheck, File, FileSearch, Eye, FileDown } from 'lucide-react';

interface PatientDetailsProps {
    patientId: number;
}

const PatientDetailsComponent: React.FC<PatientDetailsProps> = ({ patientId }) => {
    const [patient, setPatient] = useState<PatientDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();



    useEffect(() => {
        const fetchPatientDetails = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await viewPatient(patientId);
                setPatient(data);
            } catch (err: any) {
                console.error('Error fetching patient details:', err);
                setError(err.message || 'An error occurred while fetching patient details');
                toast.error(err.message || 'Failed to load patient details');
                router.push('/patient');
            } finally {
                setLoading(false);
            }
        };

        fetchPatientDetails();
    }, [patientId, router]);

    if (loading) return <Loading />;

    if (error) {
        return (
            <div className="p-4 bg-white rounded shadow">
                <div className="flex items-center justify-center h-64 text-red-500 text-lg">{error}</div>
            </div>
        );
    }

    if (!patient) {
        return (
            <div className="p-4 bg-white rounded shadow">
                <div className="flex items-center justify-center h-64 text-gray-500 text-lg">No patient data found.</div>
            </div>
        );
    }

    // Files array ko yahan declare karein
    const files = [
        { label: 'STL File 1', url: patient.stlFile1 },
        { label: 'STL File 2', url: patient.stlFile2 },
        { label: 'CBCT File', url: patient.cbctFile },
        { label: 'Profile Repose', url: patient.profileRepose },
        { label: 'Buccal Right', url: patient.buccalRight },
        { label: 'Buccal Left', url: patient.buccalLeft },
        { label: 'Frontal Repose', url: patient.frontalRepose },
        { label: 'Frontal Smiling', url: patient.frontalSmiling },
        { label: 'Labial Anterior', url: patient.labialAnterior },
        { label: 'Occlusal Lower', url: patient.occlussalLower },
        { label: 'Occlusal Upper', url: patient.occlussalUpper },
        { label: 'Radiograph 1', url: patient.radioGraph1 },
        { label: 'Radiograph 2', url: patient.radioGraph2 },
    ];

    return (
        <div className="p-6 bg-white rounded-2xl shadow-lg">
            <h2 className="text-3xl font-semibold mb-6 flex items-center gap-2 text-[#f36e22]">
                <User className="w-6 h-6" /> {patient.first_name} {patient.last_name}
            </h2>

            <div className="space-y-3 text-sm md:text-base">
                <p>
                    <strong>Doctor Name:</strong> {patient.doctor_first_name} {patient.doctor_last_name}
                </p>
                <p>
                    <strong>Plan:</strong> {patient.plan?.name} ({patient.plan?.type})
                </p>
                <p>
                    <strong>Plan Type:</strong> {patient.plan?.type}
                </p>
                <p>
                    <strong>Plan Duration:</strong> {patient.plan?.type === 'retainer' ? 'Unlimited' : `${patient.plan?.duration_years} Years`}
                </p>
                <p>
                    <strong>DOB:</strong> {new Date(patient.dob).toLocaleDateString()}
                </p>
                <p>
                    <strong>Created At:</strong> {new Date(patient.created_at).toLocaleDateString()}
                </p>
                <p>
                    <strong>Gender:</strong> {patient.gender ? patient.gender.charAt(0).toUpperCase() + patient.gender.slice(1) : ''}
                </p>
                <p>
                    <strong>Country:</strong> {patient.country ? patient.country.charAt(0).toUpperCase() + patient.country.slice(1).replace(/-/g, ' ') : ''}
                </p>
                <p>
                    <strong>Ship to Office:</strong> {patient.ship_to_office?.clinic_name}
                </p>
                <p>
                    <strong>Bill to Office:</strong> {patient.bill_to_office?.clinic_name}
                </p>
            </div>

            {/* Files Section */}
            {/* {files.some((file) => file.url) && (
                <div className="mt-8">
                    <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-gray-700">
                        <FolderOpen className="w-5 h-5" /> Uploaded Files
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        {files
                            .filter((file) => file.url)
                            .map((file, index) => {
                                const ext = file.url.split('.').pop()?.toLowerCase();
                                const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext || '');
                                const isZip = ext === 'zip';

                                return (
                                    <div key={index} className="p-3 border rounded-lg hover:shadow-md transition">
                                        <div className="flex items-center justify-between gap-2">
                                            <a href={file.url!} target="_blank" rel="noopener noreferrer" className="text-[#f36e22] flex items-center gap-2 no-underline">
                                                <File className="w-4 h-4" /> {file.label}
                                            </a>
                                            {isZip && (
                                                <a href={file.url!} download className="text-gray-600 hover:text-black">
                                                    <FileDown className="w-4 h-4" />
                                                </a>
                                            )}
                                            {isImage && (
                                                <a href={file.url!} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-black">
                                                    <Eye className="w-4 h-4" />
                                                </a>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                    </div>
                </div>
            )} */}
        </div>
    );
};

export default PatientDetailsComponent;
