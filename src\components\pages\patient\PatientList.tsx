'use client';

import React, { useState, useEffect, Fragment, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { EyeIcon } from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import Pagination from '../../../components/Pagination';
import toast from 'react-hot-toast';
import { getAllPatients, Patient, updatePatientVersionStatus } from '@/src/api/patient';
import { getAllDoctors } from '@/src/api/doctor'; // Import the API for fetching all doctors
import TableSkeleton from '../../TableSkeleton';

const PatientList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [debouncedSearch, setDebouncedSearch] = useState('');
    const [patients, setPatients] = useState<Patient[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [totalCount, setTotalCount] = useState(0);
    const [totalPages, setTotalPages] = useState(1);
    const [showModal, setShowModal] = useState(false);
    const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);

    const searchParams = useSearchParams();
    const [statusFilter, setStatusFilter] = useState<string>(() => searchParams.get('status') || 'all');
    const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);
    const [statusDropdownPosition, setStatusDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const statusDropdownRef = useRef<HTMLDivElement>(null);

    const [doctorFilter, setDoctorFilter] = useState<string>('all');
    const [doctorDropdownOpen, setDoctorDropdownOpen] = useState(false);
    const [doctorSearch, setDoctorSearch] = useState('');
    const doctorDropdownRef = useRef<HTMLDivElement>(null);

    // Debouncing effect for search
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(search);
            // setCurrentPage(1); // Reset to first page when search changes
        }, 1000); // 1000ms delay

        return () => clearTimeout(timer);
    }, [search]);

    // Close doctor dropdown on outside click
    // Update statusFilter if query param changes (e.g. via navigation)
    useEffect(() => {
        const status = searchParams.get('status');
        if (status && status !== statusFilter) {
            setStatusFilter(status);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [searchParams]);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (doctorDropdownRef.current && !doctorDropdownRef.current.contains(event.target as Node)) {
                setDoctorDropdownOpen(false);
            }
        }
        if (doctorDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [doctorDropdownOpen]);

    const router = useRouter();
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);

    // Add "Incomplete Patients" to the status options
    const statusOptions = [
        { value: 'all', label: 'All Status' },
        { value: 'sent_by_doctor', label: 'Pending' },
        { value: 'cancelled_by_admin', label: 'Cancelled' },
        { value: 'approved_by_specialist', label: 'Approved by Specialist' },
        { value: 'approved_by_doctor', label: 'Approved by Doctor' },
        { value: 'rejected_by_specialist', label: 'Rejected by Specialist' },
        { value: 'rejected_by_doctor', label: 'Rejected by Doctor' },
        { value: 'in_working', label: 'In Working' },
        { value: 'completed', label: 'Completed' },
        { value: 'incomplete', label: 'Incomplete Patients' }, // New option
    ];

    // Add state to store all doctors
    const [allDoctors, setAllDoctors] = useState<{ id: string; name: string }[]>([]);

    // Fetch all doctors on component mount
    useEffect(() => {
        const fetchAllDoctors = async () => {
            try {
                const response = await getAllDoctors({ page: 1, limit: 100 }); // Fetch first 100 doctors
                setAllDoctors(response.doctors.map((doctor: any) => ({ id: doctor.id, name: `${doctor.first_name} ${doctor.last_name}` })));
            } catch (error) {
                console.error('Error fetching doctors:', error);
                toast.error('Failed to load doctors');
            }
        };

        fetchAllDoctors();
    }, []);

    // Update the doctorOptions logic to use allDoctors
    const doctorOptions = [{ value: 'all', label: 'All Doctors' }, ...allDoctors.map((doctor) => ({ value: doctor.id, label: doctor.name }))];

    // Calculate dropdown position based on available space
    const calculateStatusDropdownPosition = () => {
        if (statusDropdownRef.current) {
            const rect = statusDropdownRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const dropdownHeight = statusOptions.length * 32 + 16;

            const spaceBelow = viewportHeight - rect.bottom;
            const spaceAbove = rect.top;

            if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
                setStatusDropdownPosition('top');
            } else {
                setStatusDropdownPosition('bottom');
            }
        }
    };

    // Close dropdown on outside click and calculate position
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
                setStatusDropdownOpen(false);
            }
        }

        function handleScroll() {
            if (statusDropdownOpen) {
                calculateStatusDropdownPosition();
            }
        }

        if (statusDropdownOpen) {
            calculateStatusDropdownPosition();
            document.addEventListener('mousedown', handleClickOutside);
            window.addEventListener('scroll', handleScroll, true);
            window.addEventListener('resize', handleScroll);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        };
    }, [statusDropdownOpen]);

    // Correct the API call to use the correct parameter name

    useEffect(() => {
        const fetchPatients = async () => {
            setLoading(true);
            try {
                setError(null);
                let apiStatus: string | undefined = undefined;
                let incomplete: boolean | undefined = undefined;

                if (statusFilter === 'incomplete') {
                    incomplete = true; // Set incomplete flag and omit `status`
                } else if (statusFilter !== 'all') {
                    apiStatus = statusFilter; // For other statuses, use the statusFilter value
                }
                const response = await getAllPatients({
                    page: currentPage,
                    limit: itemsPerPage,
                    search: debouncedSearch,
                    ...(apiStatus ? { status: apiStatus } : {}), // Include `status` only if defined
                    doctorId: doctorFilter !== 'all' ? doctorFilter : undefined, // Use `doctorId` for API call
                    ...(incomplete !== undefined ? { incomplete } : {}), // Include `incomplete` only if defined
                });
                console.log("================================>",apiStatus)
                console.log("-----000000000000000000++++++++++++++>", response)
                setPatients(response.patients || []);
                setTotalCount(response.totalCount || 0);
                setTotalPages(response.totalPages || 1);
            } catch (err: any) {
                console.error('Error fetching patients:', err);
                setError(err.message || 'An error occurred while fetching patients');
                toast.error(err.message || 'Failed to load patients');
            } finally {
                setLoading(false);
            }
        };
        fetchPatients();
    }, [statusFilter, doctorFilter, debouncedSearch, currentPage, itemsPerPage]);

    // Ensure the `statusFilter` logic correctly updates the URL
    useEffect(() => {
        const params = new URLSearchParams();

        if (statusFilter === 'incomplete') {
            params.set('incomplete', 'true');
        } else if (statusFilter !== 'all') {
            params.set('status', statusFilter);
        }

        if (doctorFilter !== 'all') {
            params.set('doctorId', doctorFilter);
        }

        if (search) {
            params.set('search', search);
        }

        router.replace(`?${params.toString()}`);
    }, [statusFilter, doctorFilter, search, router]);

    const handleView = (id: number) => router.push(`/patient/${id}`);

    const confirmStatusToggle = (patient: Patient) => {
        setSelectedPatient(patient);
        setShowModal(true);
    };

    const handleStatusToggle = async () => {
        if (!selectedPatient) return;

        const latestVersion = selectedPatient.versions?.find((v) => v.is_latest_version);
        if (!latestVersion) {
            toast.error('No latest version found for this patient');
            setShowModal(false);
            setSelectedPatient(null);
            return;
        }

        if (latestVersion.status !== 'sent_by_doctor') {
            toast.error('Only versions with "sent_by_doctor" status can be cancelled');
            setShowModal(false);
            setSelectedPatient(null);
            return;
        }

        try {
            setIsUpdating(true);
            await updatePatientVersionStatus(selectedPatient.id, latestVersion.id, false);

            setPatients((prev) =>
                prev.map((p) => {
                    if (p.id === selectedPatient.id) {
                        const updatedVersions = p.versions?.map((v) => (v.id === latestVersion.id ? { ...v, status: 'cancelled_by_admin' as const } : v)) || [];

                        return {
                            ...p,
                            versions: updatedVersions,
                        };
                    }
                    return p;
                }),
            );

            toast.success('Patient version cancelled successfully');
        } catch (err: any) {
            console.error('Error cancelling patient version:', err);
            toast.error(err.message || 'Failed to cancel patient version');
        } finally {
            setIsUpdating(false);
            setShowModal(false);
            setSelectedPatient(null);
        }
    };

    // Define column widths for the skeleton loader to match the table layout
    const TABLE_COL_WIDTHS = [
        'w-40', // Name
        'w-40', // Doctor Name
        'w-40', // Plan
        'w-40', // Plan Duration
        'w-40', // DOB
        'w-40', // Created At
        'w-40', // Country
        'w-40', // Status
        'w-40', // Actions
    ];

    // Use backend data directly
    const getPaginatedData = () => patients;

    useEffect(() => {console.log("-----___________++++++++++++++>", loading)}, [loading])

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Patient List</h2>
            </div>

            {/* Filters */}
            <div className="flex items-center justify-end w-full pb-4 gap-4">
                {/* Status Filter */}
                <div className="flex gap-4 items-center">
                    <div ref={statusDropdownRef} className="relative min-h-[33px]">
                        <button
                            type="button"
                            className="border border-gray-300 rounded-lg px-2 py-2 text-sm min-w-[180px] bg-white text-black flex items-center justify-between cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#eb6309]"
                            onClick={() => setStatusDropdownOpen((open) => !open)}
                        >
                            {statusOptions.find((option) => option.value === statusFilter)?.label}
                            <span className="ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </span>
                        </button>
                        {statusDropdownOpen && (
                            <div className={`absolute left-0 min-w-full z-20 bg-white border rounded shadow-lg ${statusDropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'}`}>
                                {statusOptions.map((option, index) => (
                                    <div
                                        key={option.value}
                                        className={`px-4 py-2 cursor-pointer
                                        ${index === 0 ? 'rounded-t-sm' : ''}
                                        ${index === statusOptions.length - 1 ? 'rounded-b-sm' : ''}
                                        ${index < statusOptions.length - 1 ? 'border-b border-gray-200' : ''}
                                        ${statusFilter === option.value ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'}
                                        hover:bg-[#eb6309] hover:text-white`}
                                        onClick={() => {
                                            setStatusFilter(option.value);
                                            setStatusDropdownOpen(false);
                                        }}
                                    >
                                        {option.label}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                {/* Doctor Filter */}
                <div ref={doctorDropdownRef} className="relative min-h-[33px]">
                    <button
                        type="button"
                        className="border border-gray-300 rounded-lg px-2 py-2 text-sm min-w-[180px] bg-white text-black flex items-center justify-between cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#eb6309]"
                        onClick={() => setDoctorDropdownOpen((open) => !open)}
                    >
                        {doctorOptions.find((d) => d.value === doctorFilter)?.label}
                        <span className="ml-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                        </span>
                    </button>
                    {doctorDropdownOpen && (
                        <div className="absolute left-0 min-w-full z-20 bg-white border rounded shadow-lg top-full mt-1 max-h-64 overflow-y-auto">
                            <div className="p-2 border-b">
                                <input
                                    type="text"
                                    placeholder="Search doctor..."
                                    value={doctorSearch}
                                    onChange={(e) => setDoctorSearch(e.target.value)}
                                    className="w-full border rounded px-2 py-1 text-sm"
                                />
                            </div>
                            {doctorOptions
                                .filter((opt) => opt.label.toLowerCase().includes(doctorSearch.toLowerCase()))
                                .map((option) => (
                                    <div
                                        key={option.value}
                                        className={`px-4 py-2 cursor-pointer border-b last:border-0
                                            ${doctorFilter === option.value ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'}
                                            hover:bg-[#eb6309] hover:text-white`}
                                        onClick={() => {
                                            setDoctorFilter(option.value);
                                            setDoctorDropdownOpen(false);
                                            setDoctorSearch(''); // Clear search when selection is made
                                        }}
                                    >
                                        {option.label}
                                    </div>
                                ))}
                        </div>
                    )}
                </div>

                {/* Search box */}
                <input type="text" placeholder="Search" value={search} onChange={(e) => setSearch(e.target.value)} className="form-input !w-1/4 text-left" />
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="py-3 px-4 text-start font-bold text-lg">Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Doctor Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Plan</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Plan Duration</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">DOB</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Created At</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Country</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Status</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            <TableSkeleton rows={itemsPerPage} colWidths={TABLE_COL_WIDTHS} />
                        ) : patients.length === 0 ? (
                            <tr>
                                <td colSpan={TABLE_COL_WIDTHS.length} className="py-8 px-4 text-center text-gray-500 text-lg">
                                    No records found
                                </td>
                            </tr>
                        ) : (
                            patients.map((row: Patient, idx: number) => {
                                const latestVersion = row.versions?.find((v) => v.is_latest_version);
                                const versionStatus = latestVersion?.status;
                                const canCancel = versionStatus === 'sent_by_doctor';

                                return (
                                    <tr key={row.id} className={`hover:bg-gray-50 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${versionStatus === 'cancelled_by_admin' ? 'opacity-60' : ''}`}>
                                        <td className="py-3 px-4 text-sm text-gray-800">
                                            {row.first_name} {row.last_name}
                                        </td>
                                        <td className="py-3 px-4 text-sm text-gray-800">
                                            {row.doctor_first_name && row.doctor_last_name
                                                ? `${row.doctor_first_name} ${row.doctor_last_name}`
                                                : 'N/A'}
                                        </td>
                                        <td className="py-3 px-4 text-sm text-gray-800">{row.plan_name || row.plan?.name || 'N/A'}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800">{row.plan_type === 'retainer' ? 'Unlimited' : `${row?.plan_duration_years} Years`}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800">{row.dob}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800">{row.created_at}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800">{row.country ? row.country.charAt(0).toUpperCase() + row.country.slice(1).replace('-', ' ') : ''}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800">
                                            <div className="flex items-center gap-2">
                                                <span
                                                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                        versionStatus === 'sent_by_doctor'
                                                            ? 'bg-yellow-100 text-yellow-800'
                                                            : versionStatus === 'cancelled_by_admin'
                                                              ? 'bg-red-100 text-red-800'
                                                              : versionStatus === 'approved_by_specialist'
                                                                ? 'bg-blue-100 text-blue-800'
                                                                : versionStatus === 'approved_by_doctor'
                                                                  ? 'bg-green-100 text-green-800'
                                                                  : versionStatus === 'rejected_by_specialist'
                                                                    ? 'bg-orange-100 text-orange-800'
                                                                    : versionStatus === 'rejected_by_doctor'
                                                                      ? 'bg-rose-100 text-rose-800'
                                                                      : versionStatus === 'in_working'
                                                                        ? 'bg-purple-100 text-purple-800'
                                                                        : versionStatus === 'completed'
                                                                          ? 'bg-emerald-100 text-emerald-800'
                                                                          : 'bg-gray-100 text-gray-800'
                                                    }`}
                                                >
                                                    {versionStatus === 'sent_by_doctor'
                                                        ? 'Pending'
                                                        : versionStatus === 'cancelled_by_admin'
                                                          ? 'Cancelled'
                                                          : versionStatus === 'approved_by_specialist'
                                                            ? 'Approved by Specialist'
                                                            : versionStatus === 'approved_by_doctor'
                                                              ? 'Approved by Doctor'
                                                              : versionStatus === 'rejected_by_specialist'
                                                                ? 'Rejected By Specialist'
                                                                : versionStatus === 'rejected_by_doctor'
                                                                  ? 'Rejected By Doctor'
                                                                  : versionStatus === 'in_working'
                                                                    ? 'In Working'
                                                                    : versionStatus === 'completed'
                                                                      ? 'Completed'
                                                                      : row.versions?.length === 0
                                                                        ? 'No Version'
                                                                        : 'Unknown'}
                                                </span>
                                                {canCancel && (
                                                    <button
                                                        onClick={() => confirmStatusToggle(row)}
                                                        className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 bg-[#f36f22] hover:bg-[#d85a1c]"
                                                        title="Cancel latest version"
                                                    >
                                                        <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                                                    </button>
                                                )}
                                            </div>
                                        </td>
                                        <td className="py-3 px-4 text-sm text-gray-800">
                                            <div className="flex gap-3 items-center">
                                                <button onClick={() => handleView(row.id)} title="View">
                                                    <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                );
                            })
                        )}
                    </tbody>
                </table>
            </div>

            <Pagination currentPage={currentPage} totalItems={totalCount} itemsPerPage={itemsPerPage} onPageChange={setCurrentPage} onItemsPerPageChange={setItemsPerPage} />

            {/* Status Toggle Confirmation Modal */}
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-25" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    {(() => {
                                        const latestVersion = selectedPatient?.versions?.find((v) => v.is_latest_version) || null;
                                        const canCancel = latestVersion?.status === 'sent_by_doctor';

                                        return (
                                            <div>
                                                {latestVersion && (
                                                    <>
                                                        <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                                            Cancel Patient Version
                                                        </Dialog.Title>
                                                        <div className="mt-2">
                                                            <p className="text-sm text-gray-500">
                                                                Are you sure you want to cancel version
                                                                {latestVersion && ` ${latestVersion.version_number}`} for{' '}
                                                                <span className="font-semibold">
                                                                    {selectedPatient?.first_name} {selectedPatient?.last_name}
                                                                </span>
                                                                ? This action will mark the version as cancelled and cannot be undone.
                                                            </p>
                                                            {!canCancel && <p className="text-sm text-red-600 mt-2">Note: Only versions with "sent_by_doctor" status can be cancelled.</p>}
                                                        </div>

                                                        <div className="mt-4 flex justify-end gap-3">
                                                            <button className="px-4 py-2 border rounded hover:bg-gray-50 text-black" onClick={() => setShowModal(false)} disabled={isUpdating}>
                                                                Cancel
                                                            </button>
                                                            <button
                                                                className={`px-4 py-2 rounded text-white bg-red-600 hover:bg-red-700 ${isUpdating || !canCancel ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                                onClick={handleStatusToggle}
                                                                disabled={isUpdating || !canCancel}
                                                            >
                                                                {isUpdating ? 'Cancelling...' : 'Cancel Version'}
                                                            </button>
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        );
                                    })()}
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default PatientList;
