'use client';

import React, { useState, useEffect, Fragment } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { deletePlan, getAllPlans, Plan } from '@/src/api/plans';
import toast from 'react-hot-toast';
import TableSkeleton from '../../TableSkeleton';
import Pagination from '../../Pagination';
import { Dialog, Transition } from '@headlessui/react';

const PlansList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [debouncedSearch, setDebouncedSearch] = useState('');
    const [plans, setPlans] = useState<Plan[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [totalCount, setTotalCount] = useState(0);
    const [totalPages, setTotalPages] = useState(1);
    const [showModal, setShowModal] = useState(false);
    const [selectedId, setSelectedId] = useState<number | null>(null);
    const router = useRouter();
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);

    // Debouncing effect for search
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(search.trim());
            setCurrentPage(1); // Reset to first page when search changes
        }, 500); // 500ms delay

        return () => clearTimeout(timer);
    }, [search]);

    const TABLE_COL_WIDTHS = [
        'w-56', // Name
        'w-40', // Type
        'w-40', // Duration (years)
        'w-32', // Actions
    ];

    // Fetch plans from API with backend search
    useEffect(() => {
        const fetchPlans = async () => {
            try {
                setLoading(true);
                setError(null);

                // Prepare search parameters - only include search if it's not empty
                const searchParams: { search?: string; page: number; limit: number } = {
                    page: currentPage,
                    limit: itemsPerPage,
                };

                if (debouncedSearch && debouncedSearch.trim() !== '') {
                    searchParams.search = debouncedSearch.trim();
                }

                const response = await getAllPlans(searchParams);
                setPlans(response.plans || []);
                setTotalCount(response.totalCount || 0);
                setTotalPages(response.totalPages || 1);
            } catch (err: any) {
                setError(err.message || 'An error occurred while fetching plans');
                console.error('Error fetching plans:', err);
            } finally {
                setLoading(false);
            }
        };
        fetchPlans();
    }, [debouncedSearch, currentPage, itemsPerPage]);

    const confirmDelete = (id: number) => {
        setSelectedId(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (selectedId === null) return;

        try {
            await deletePlan(selectedId);

            // Update local state
            const updatedPlans = plans.filter((d) => d.id !== selectedId);
            setPlans(updatedPlans);
            const newTotalCount = totalCount - 1;
            setTotalCount(newTotalCount);

            // Check if we need to adjust the current page
            // If we deleted the last item on the current page and it's not the first page
            if (updatedPlans.length === 0 && currentPage > 1) {
                setCurrentPage(currentPage - 1);
            }

            toast.success('Plan deleted successfully');
        } catch (err: any) {
            console.error('Error deleting plan:', err);
            toast.error(err.message || 'An error occurred while deleting the plan');
        } finally {
            setShowModal(false);
            setSelectedId(null);
        }
    };

    const handleView = (id: number) => router.push(`/plans/${id}?mode=view`);
    const handleEdit = (id: number) => router.push(`/plans/${id}?mode=edit`);

    // Use backend data directly, no frontend filtering
    const getPaginatedData = () => plans;

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Plans List</h2>
                <button
                    onClick={() => router.push('/plans/add')}
                    className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]"
                >
                    Add Plan
                </button>
            </div>

            {/* Search input after Add Plan button */}
            <div className="mb-4 flex justify-end">
                <input type="text" placeholder="Search" value={search} onChange={(e) => setSearch(e.target.value)} className="form-input !w-1/4 text-left" />
            </div>

            {/* Error message */}
            {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {error}
                </div>
            )}

            {/* Custom Table */}
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="py-3 px-4 text-start font-bold text-lg w-56">Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg w-40">Type</th>
                            <th className="py-3 px-4 text-start font-bold text-lg w-40">Duration (years)</th>
                            <th className="py-3 px-4 text-start font-bold text-lg w-32">Actions</th>
                        </tr>
                    </thead>
                    {loading ? (
                        <TableSkeleton rows={itemsPerPage} colWidths={TABLE_COL_WIDTHS} />
                    ) : (
                        <tbody>
                            {getPaginatedData().length === 0 ? (
                                <tr>
                                    <td colSpan={4} className="py-8 px-4 text-center text-gray-500 text-lg">
                                        No records found
                                    </td>
                                </tr>
                            ) : (
                                getPaginatedData().map((row: Plan, idx: number) => (
                                    <tr key={row.id} className={`hover:bg-gray-50 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                                        <td className="py-3 px-4 text-sm text-gray-800 w-56">{row.name}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800 w-40">{row.type}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800 w-40">{row.duration_years ?? '-'}</td>
                                        <td className="py-3 px-4 text-sm text-gray-800 w-32">
                                            <div className="flex gap-3 items-center">
                                                <button onClick={() => handleView(row.id)} title="View">
                                                    <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                                                </button>
                                                <button onClick={() => handleEdit(row.id)} title="Edit">
                                                    <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
                                                </button>
                                                <button onClick={() => confirmDelete(row.id)} title="Delete">
                                                    <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    )}
                </table>
            </div>

            {/* Pagination */}
            <Pagination currentPage={currentPage} totalItems={totalCount} itemsPerPage={itemsPerPage} onPageChange={setCurrentPage} onItemsPerPageChange={setItemsPerPage} />

            {/* Confirmation Modal with Headless UI */}
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        Confirm Deletion
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">Are you sure you want to delete this plan?</div>

                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={() => setShowModal(false)}>
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-[#eb6309] text-white rounded hover:bg-[#fee2e2] hover:text-[#eb6309]" onClick={handleDelete}>
                                            Delete
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default PlansList;
