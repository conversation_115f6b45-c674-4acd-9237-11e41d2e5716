import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authService, User } from '@/src/api/auth';
import toast from 'react-hot-toast';
import Loading from '@/src/components/layouts/loading';
import { baseUrl } from '@/src/utils/apiRoutes';
import { useAuth } from '@/src/contexts/AuthContext';

// ChangePasswordForm component
const ChangePasswordForm = () => {
    const [currentPassword, setCurrentPassword] = useState('');
    const [password, setPassword] = useState('');
    const [passwordConfirmation, setPasswordConfirmation] = useState('');
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [loading, setLoading] = useState(false);
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const validate = () => {
        const newErrors: { [key: string]: string } = {};

        // Current password validation
        if (!currentPassword) newErrors.currentPassword = 'Current password is required';

        // New password validation
        if (!password) {
            newErrors.password = 'New password is required';
        } else {
            // Password strength validations
            if (password.length < 8) {
                newErrors.password = 'Password must be at least 8 characters long';
            } else if (!/[A-Z]/.test(password)) {
                newErrors.password = 'Password must contain at least one uppercase letter';
            } else if (!/[0-9]/.test(password)) {
                newErrors.password = 'Password must contain at least one number';
            } else if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
                newErrors.password = 'Password must contain at least one special character';
            }
        }

        // Password confirmation validation
        if (!passwordConfirmation) {
            newErrors.passwordConfirmation = 'Please confirm your new password';
        } else if (password && passwordConfirmation && password !== passwordConfirmation) {
            newErrors.passwordConfirmation = 'Passwords do not match';
        }

        return newErrors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const validationErrors = validate();
        setErrors(validationErrors);
        if (Object.keys(validationErrors).length > 0) return;
        setLoading(true);
        try {
            await authService.changePassword(currentPassword, password, passwordConfirmation);
            toast.success('Password changed successfully!');
            setCurrentPassword('');
            setPassword('');
            setPasswordConfirmation('');
            setErrors({}); // Clear any previous errors
        } catch (err: any) {
            // Now we only need to handle non-validation errors (like wrong current password)
            toast.error(err.message || 'Failed to change password');
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md max-w-lg">
            <div className="mb-4">
                <label htmlFor="currentPassword" className="font-medium">
                    Current Password:
                </label>
                <div className="relative">
                    <input
                        id="currentPassword"
                        name="currentPassword"
                        type={showCurrentPassword ? 'text' : 'password'}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        className={`border p-2 w-full rounded-md mt-1 ${currentPassword ? 'pr-10' : ''}`}
                    />
                    {currentPassword && (
                        <button
                            type="button"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            {showCurrentPassword ? (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M1.7070312 0.29296875L0.29296875 1.7070312L28.292969 29.707031L29.707031 28.292969L23.681641 22.267578C27.777456 19.49434 29.799165 15.616636 29.826172 15.564453 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5C12.469857 5 10.199331 5.7501922 8.234375 6.8203125L1.7070312 0.29296875 z M 15 8C18.866 8 22 11.134 22 15C22 16.571956 21.470043 18.012848 20.59375 19.179688L17.701172 16.287109C17.889655 15.897819 18 15.462846 18 15C18 13.343 16.657 12 15 12C14.537154 12 14.102181 12.110345 13.712891 12.298828L10.820312 9.40625C11.987152 8.5299565 13.428044 8 15 8 z M 4.9511719 9.0761719C1.9791583 11.576125 0.27498083 14.287031 0.21875 14.376953 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C16.85 25 18.520531 24.673484 20.019531 24.146484L17.431641 21.556641C16.672641 21.838641 15.856 22 15 22C11.134 22 8 18.866 8 15C8 14.144 8.1613594 13.327359 8.4433594 12.568359L4.9511719 9.0761719 z" />
                                </svg>
                            ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M15 5C6.081703 5 0.32098813 14.21118 0.21679688 14.378906 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C24.938822 25 29.767326 15.678741 29.826172 15.564453 A 1 1 0 0 0 29.837891 15.544922 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.785156 14.380859 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5 z M 15 8C18.866 8 22 11.134 22 15C22 18.866 18.866 22 15 22C11.134 22 8 18.866 8 15C8 11.134 11.134 8 15 8 z M 15 12 A 3 3 0 0 0 12 15 A 3 3 0 0 0 15 18 A 3 3 0 0 0 18 15 A 3 3 0 0 0 15 12 z" />
                                </svg>
                            )}
                        </button>
                    )}
                </div>
                {errors.currentPassword && <div className="text-red-500 text-sm mt-1">{errors.currentPassword}</div>}
            </div>
            <div className="mb-4">
                <label htmlFor="password" className="font-medium">
                    New Password:
                </label>
                <div className="relative">
                    <input
                        id="password"
                        name="password"
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className={`border p-2 w-full rounded-md mt-1 ${password ? 'pr-10' : ''}`}
                    />
                    {password && (
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            {showPassword ? (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M1.7070312 0.29296875L0.29296875 1.7070312L28.292969 29.707031L29.707031 28.292969L23.681641 22.267578C27.777456 19.49434 29.799165 15.616636 29.826172 15.564453 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5C12.469857 5 10.199331 5.7501922 8.234375 6.8203125L1.7070312 0.29296875 z M 15 8C18.866 8 22 11.134 22 15C22 16.571956 21.470043 18.012848 20.59375 19.179688L17.701172 16.287109C17.889655 15.897819 18 15.462846 18 15C18 13.343 16.657 12 15 12C14.537154 12 14.102181 12.110345 13.712891 12.298828L10.820312 9.40625C11.987152 8.5299565 13.428044 8 15 8 z M 4.9511719 9.0761719C1.9791583 11.576125 0.27498083 14.287031 0.21875 14.376953 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C16.85 25 18.520531 24.673484 20.019531 24.146484L17.431641 21.556641C16.672641 21.838641 15.856 22 15 22C11.134 22 8 18.866 8 15C8 14.144 8.1613594 13.327359 8.4433594 12.568359L4.9511719 9.0761719 z" />
                                </svg>
                            ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M15 5C6.081703 5 0.32098813 14.21118 0.21679688 14.378906 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C24.938822 25 29.767326 15.678741 29.826172 15.564453 A 1 1 0 0 0 29.837891 15.544922 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.785156 14.380859 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5 z M 15 8C18.866 8 22 11.134 22 15C22 18.866 18.866 22 15 22C11.134 22 8 18.866 8 15C8 11.134 11.134 8 15 8 z M 15 12 A 3 3 0 0 0 12 15 A 3 3 0 0 0 15 18 A 3 3 0 0 0 18 15 A 3 3 0 0 0 15 12 z" />
                                </svg>
                            )}
                        </button>
                    )}
                </div>
                {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}
            </div>
            <div className="mb-6">
                <label htmlFor="passwordConfirmation" className="font-medium">
                    Confirm New Password:
                </label>
                <div className="relative">
                    <input
                        id="passwordConfirmation"
                        name="passwordConfirmation"
                        type={showPasswordConfirmation ? 'text' : 'password'}
                        value={passwordConfirmation}
                        onChange={(e) => setPasswordConfirmation(e.target.value)}
                        className={`border p-2 w-full rounded-md mt-1 ${passwordConfirmation ? 'pr-10' : ''}`}
                    />
                    {passwordConfirmation && (
                        <button
                            type="button"
                            onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            {showPasswordConfirmation ? (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M1.7070312 0.29296875L0.29296875 1.7070312L28.292969 29.707031L29.707031 28.292969L23.681641 22.267578C27.777456 19.49434 29.799165 15.616636 29.826172 15.564453 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5C12.469857 5 10.199331 5.7501922 8.234375 6.8203125L1.7070312 0.29296875 z M 15 8C18.866 8 22 11.134 22 15C22 16.571956 21.470043 18.012848 20.59375 19.179688L17.701172 16.287109C17.889655 15.897819 18 15.462846 18 15C18 13.343 16.657 12 15 12C14.537154 12 14.102181 12.110345 13.712891 12.298828L10.820312 9.40625C11.987152 8.5299565 13.428044 8 15 8 z M 4.9511719 9.0761719C1.9791583 11.576125 0.27498083 14.287031 0.21875 14.376953 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C16.85 25 18.520531 24.673484 20.019531 24.146484L17.431641 21.556641C16.672641 21.838641 15.856 22 15 22C11.134 22 8 18.866 8 15C8 14.144 8.1613594 13.327359 8.4433594 12.568359L4.9511719 9.0761719 z" />
                                </svg>
                            ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="20" height="20" className="fill-current">
                                    <path d="M15 5C6.081703 5 0.32098813 14.21118 0.21679688 14.378906 A 1 1 0 0 0 0 15 A 1 1 0 0 0 0.16210938 15.544922 A 1 1 0 0 0 0.16601562 15.550781C0.18320928 15.586261 5.0188313 25 15 25C24.938822 25 29.767326 15.678741 29.826172 15.564453 A 1 1 0 0 0 29.837891 15.544922 A 1 1 0 0 0 30 15 A 1 1 0 0 0 29.785156 14.380859 A 1 1 0 0 0 29.783203 14.378906C29.679012 14.21118 23.918297 5 15 5 z M 15 8C18.866 8 22 11.134 22 15C22 18.866 18.866 22 15 22C11.134 22 8 18.866 8 15C8 11.134 11.134 8 15 8 z M 15 12 A 3 3 0 0 0 12 15 A 3 3 0 0 0 15 18 A 3 3 0 0 0 18 15 A 3 3 0 0 0 15 12 z" />
                                </svg>
                            )}
                        </button>
                    )}
                </div>
                {errors.passwordConfirmation && <div className="text-red-500 text-sm mt-1">{errors.passwordConfirmation}</div>}
            </div>
            <button type="submit" className="bg-[#ef7125] text-white px-6 py-2 rounded-full hover:bg-[#fee2e2] hover:text-[#ef7125] transition-colors duration-200" disabled={loading}>
                {loading ? 'Saving...' : 'Change Password'}
            </button>
        </form>
    );
};

const ProfilePage = () => {
    const router = useRouter();
    const { refreshUser, updateUser: contextUpdateUser } = useAuth();
    const [profile, setProfile] = useState<Partial<User>>({});
    const [loading, setLoading] = useState(true);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [imageError, setImageError] = useState(false);

    // Helper function to construct proper image URL
    const getImageUrl = (imagePath: string): string => {
        if (!imagePath) return '';

        // If it's already a full URL or starts with /, return as is
        if (imagePath.startsWith('http') || imagePath.startsWith('https') || imagePath.startsWith('/')) {
            return imagePath;
        }

        // If it's a blob URL (for preview), return as is
        if (imagePath.startsWith('blob:')) {
            return imagePath;
        }

        // For relative paths, construct full URL
        const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        const cleanImagePath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;

        console.log('Base URL:', cleanBaseUrl);
        console.log('Image Path:', imagePath);
        console.log('Final URL:', `${cleanBaseUrl}${cleanImagePath}`);

        return `${cleanBaseUrl}${cleanImagePath}`;
    };
    useEffect(() => {
        const fetchProfile = async () => {
            try {
                const data = await authService.getProfile();
                setProfile(data);
            } catch (err: any) {
                toast.error(err.message || 'Failed to load profile');
            } finally {
                setLoading(false);
            }
        };
        fetchProfile();
    }, []);

    const validate = (fields = profile, image = imageFile) => {
        const newErrors: { [key: string]: string } = {};
        if (!fields.first_name) newErrors.first_name = 'First name is required';
        if (!fields.last_name) newErrors.last_name = 'Last name is required';
        if (!fields.email) newErrors.email = 'Email is required';
        if (!fields.profile_image && !image) newErrors.profile_image = 'Profile image is required';
        return newErrors;
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setProfile((prev) => {
            const updated = { ...prev, [name]: value };
            setErrors(validate(updated, imageFile));
            return updated;
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            setImageError(false); // Reset image error when new file is selected
            const previewUrl = URL.createObjectURL(file);
            setProfile((prev) => {
                const updated = { ...prev, profile_image: previewUrl };
                setErrors(validate(updated, file));
                return updated;
            });
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const validationErrors = validate(profile, imageFile);
        setErrors(validationErrors);
        if (Object.keys(validationErrors).length > 0) {
            setLoading(false);
            return;
        }
        setLoading(true);
        try {
            const formData = new FormData();
            Object.entries(profile).forEach(([key, value]) => {
                if (key !== 'profile_image' && value) {
                    formData.append(key, value as string);
                }
            });
            if (imageFile) {
                formData.append('profile_image', imageFile);
            }
            formData.append('_method', 'PUT');
            const updatedUser = await authService.updateProfile(formData);
            toast.success('Profile updated successfully!');
            contextUpdateUser(updatedUser);
            setProfile(updatedUser); // Update local profile state
            setImageFile(null); // Clear the selected file
            setImageError(false); // Reset image error state
        } catch (err: any) {
            toast.error(err.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    if (loading) return <Loading />;

    return (
        <>
            <h2 className="text-2xl font-bold mb-4">Edit Profile</h2>
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="first_name" className="font-medium">
                            First Name:
                        </label>
                        <input id="first_name" name="first_name" value={profile.first_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.first_name && <div className="text-red-500 text-sm mt-1">{errors.first_name}</div>}
                    </div>
                    <div>
                        <label htmlFor="last_name" className="font-medium">
                            Last Name:
                        </label>
                        <input id="last_name" name="last_name" value={profile.last_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.last_name && <div className="text-red-500 text-sm mt-1">{errors.last_name}</div>}
                    </div>
                    <div>
                        <label htmlFor="email" className="font-medium">
                            Email:
                        </label>
                        <input id="email" name="email" type="email" value={profile.email || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}
                    </div>
                    <div className="col-span-1 md:col-span-2">
                        <label htmlFor="profile_image" className="font-medium">
                            Profile Image:
                        </label>
                        <div className="flex items-center gap-4 mt-1">
                            {profile.profile_image && (
                                profile.profile_image.startsWith('blob:') ? (
                                    <Image src={profile.profile_image} alt="Profile preview" width={96} height={96} className="h-24 w-24 rounded-full object-cover" unoptimized />
                                ) : (
                                    <Image
                                        src={profile.profile_image.startsWith('http') ? profile.profile_image : `${profile.profile_image}`}
                                        alt="Profile"
                                        width={96}
                                        height={96}
                                        className="h-24 w-24 rounded-full object-cover"
                                    />
                                )
                            )}
                            <input id="profile_image" name="profile_image" type="file" onChange={handleFileChange} className="border p-2 w-full rounded-md" />
                            {errors.profile_image && <div className="text-red-500 text-sm mt-1">{errors.profile_image}</div>}
                        </div>
                    </div>
                </div>
                <div className="flex gap-4 mt-6">
                    <button type="submit" className="bg-[#ef7125] text-white px-6 py-2 rounded-full hover:bg-[#fee2e2] hover:text-[#ef7125] transition-colors duration-200" disabled={loading}>
                        {loading ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button type="button" onClick={() => router.back()} className="border border-gray-400 text-gray-700 px-6 py-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </form>
            {/* Change Password Form */}
            <div className="mt-10">
                <h2 className="text-2xl font-bold mb-4">Change Password</h2>
                <ChangePasswordForm />
            </div>
        </>
    );
};

export default ProfilePage;
