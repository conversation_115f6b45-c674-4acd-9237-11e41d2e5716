'use client';

import React, { useState, useEffect, Fragment, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';
import { getAllSpecialists, deleteSpecialist, Specialist } from '@/src/api/specialist';
import { toggleSpecialistStatus, restoreSpecialist } from '@/src/api/specialistStatus';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';
import Pagination from '../../Pagination';

const TABLE_COL_WIDTHS = [
    'w-40', //  Name
    'w-56', // Email
    'w-40', // Username
    'w-32', // Status
    'w-32', // Actions
];

const SpecialistList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [debouncedSearch, setDebouncedSearch] = useState('');
    const [specialists, setSpecialists] = useState<Specialist[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [selectedId, setSelectedId] = useState<number | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [totalItems, setTotalItems] = useState(0);
    const [totalPages, setTotalPages] = useState(1);
    const [showRestoreModal, setShowRestoreModal] = useState(false);
    const [showToggleModal, setShowToggleModal] = useState(false);
    const [selectedSpecialist, setSelectedSpecialist] = useState<Specialist | null>(null);
    const [isToggling, setIsToggling] = useState(false);
    const confirmRestore = (id: number) => {
        setSelectedId(id);
        setShowRestoreModal(true);
    };

    const confirmToggleStatus = (specialist: Specialist) => {
        setSelectedSpecialist(specialist);
        setShowToggleModal(true);
    };

    const handleRestore = async () => {
        if (selectedId === null) return;
        try {
            await restoreSpecialist(selectedId);
            // Refetch the current filter to update the list
            const params = { page: currentPage, limit: itemsPerPage, filter: filterStatus };
            if (debouncedSearch) (params as any).search = debouncedSearch;
            const result = await getAllSpecialists(params);
            setSpecialists(result.specialists);
            toast.success('Specialist restored successfully');
        } catch (err: any) {
            toast.error(err.message || 'An error occurred while restoring the specialist');
        } finally {
            setShowRestoreModal(false);
            setSelectedId(null);
        }
    };

    const handleToggleStatus = async () => {
        if (!selectedSpecialist) return;
        try {
            setIsToggling(true);
            const newStatus = !selectedSpecialist.is_active;
            await toggleSpecialistStatus(selectedSpecialist.id, newStatus);
            // Refetch the current filter to update the list
            const params = { page: currentPage, limit: itemsPerPage, filter: filterStatus };
            if (debouncedSearch) (params as any).search = debouncedSearch;
            const result = await getAllSpecialists(params);
            setSpecialists(result.specialists);
            toast.success(`Specialist ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } catch (err: any) {
            toast.error(err.message || 'An error occurred while updating specialist status');
        } finally {
            setIsToggling(false);
            setShowToggleModal(false);
            setSelectedSpecialist(null);
        }
    };
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchParams = useSearchParams();
    const initialFilter = searchParams?.get('filter') as 'all' | 'active' | 'inactive' | 'deleted' | null;
    const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'deleted'>(initialFilter || 'all');
    const router = useRouter();

    // Debounce search input
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(search);
        }, 1000);

        return () => clearTimeout(timer);
    }, [search]);

    // Reset to first page when search changes
    useEffect(() => {
        if (debouncedSearch !== search) return; // Only reset page when debounced search is updated
        // setCurrentPage(1);
    }, [debouncedSearch]);

    useEffect(() => {
        const params = new URLSearchParams(searchParams.toString());
        params.set('filter', filterStatus);
        router.replace(`?${params.toString()}`);
    }, [filterStatus]);

    // Fetch specialists from API with search support
    useEffect(() => {
        const fetchSpecialists = async () => {
            try {
                setLoading(true);
                setError(null);
                let apiFilter: 'all' | 'active' | 'inactive' | 'deleted' | undefined =
                    filterStatus === 'all' ? 'all' : filterStatus === 'active' ? 'active' : filterStatus === 'inactive' ? 'inactive' : 'deleted';
                const params = { page: currentPage, limit: itemsPerPage };
                if (apiFilter) (params as any).filter = apiFilter;
                if (debouncedSearch) (params as any).search = debouncedSearch;
                const result = await getAllSpecialists(params);
                setSpecialists(result.specialists);
                setTotalItems(result.totalCount || result.specialists.length);
                setTotalPages(result.totalPages || Math.ceil(result.specialists.length / itemsPerPage));
            } catch (err: any) {
                console.error('Error fetching specialists:', err);
                setError(err.message || 'An error occurred while fetching specialists');
            } finally {
                setLoading(false);
            }
        };
        fetchSpecialists();
    }, [filterStatus, currentPage, itemsPerPage, debouncedSearch]);

    // Close dropdown on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        }
        if (dropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [dropdownOpen]);

    const confirmDelete = (id: number) => {
        setSelectedId(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (selectedId === null) return;
        try {
            await deleteSpecialist(selectedId);
            setSpecialists((prev) => prev.filter((d) => d.id !== selectedId));
            toast.success('Specialist deleted successfully');
        } catch (err: any) {
            toast.error(err.message || 'An error occurred while deleting the specialist');
        } finally {
            setShowModal(false);
            setSelectedId(null);
        }
    };

    const handleView = (id: number) => router.push(`/specialist/${id}?mode=view`);
    const handleEdit = (id: number) => router.push(`/specialist/${id}?mode=edit`);

    // Backend handles filtering, pagination, and search; use data directly from API
    const getPaginatedData = () => specialists;

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">
                    {filterStatus === 'all'
                        ? 'All Specialists List'
                        : filterStatus === 'active'
                          ? 'Active Specialists List'
                          : filterStatus === 'inactive'
                            ? 'Inactive Specialists List'
                            : 'Deleted Specialists List'}
                </h2>
                <button
                    onClick={() => router.push('/specialist/add')}
                    className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]"
                >
                    Add Specialist
                </button>
            </div>
            {/* Search input and Status filter */}
            <div className="flex items-center justify-end w-full pb-4 gap-4">
                <div className="flex gap-4 items-center">
                    <div ref={dropdownRef} className="relative min-h-[33px]">
                        <button
                            type="button"
                            className="border border-gray-300 rounded-lg px-2 py-2 text-sm min-w-[180px] bg-white text-black flex items-center justify-between cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#eb6309]"
                            onClick={() => setDropdownOpen((open) => !open)}
                        >
                            {filterStatus === 'all'
                                ? 'All Specialists'
                                : filterStatus === 'active'
                                  ? 'Active Specialists'
                                  : filterStatus === 'inactive'
                                    ? 'Inactive Specialists'
                                    : 'Deleted Specialists'}
                            <span className="ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </span>
                        </button>
                        {dropdownOpen && (
                            <div className="absolute left-0 min-w-full z-20 bg-white border rounded shadow-lg top-full mt-1">
                                <div
                                    className={`px-4 py-2 cursor-pointer rounded-t-sm ${filterStatus === 'all' ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'} hover:bg-[#eb6309] hover:text-white border-b border-gray-200`}
                                    onClick={() => {
                                        setFilterStatus('all');
                                        setDropdownOpen(false);
                                    }}
                                >
                                    All Specialists
                                </div>
                                <div
                                    className={`px-4 py-2 cursor-pointer ${filterStatus === 'active' ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'} hover:bg-[#eb6309] hover:text-white border-b border-gray-200`}
                                    onClick={() => {
                                        setFilterStatus('active');
                                        setDropdownOpen(false);
                                    }}
                                >
                                    Active Specialists
                                </div>
                                <div
                                    className={`px-4 py-2 cursor-pointer ${filterStatus === 'inactive' ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'} hover:bg-[#eb6309] hover:text-white border-b border-gray-200`}
                                    onClick={() => {
                                        setFilterStatus('inactive');
                                        setDropdownOpen(false);
                                    }}
                                >
                                    Inactive Specialists
                                </div>
                                <div
                                    className={`px-4 py-2 cursor-pointer rounded-b-sm whitespace-nowrap ${filterStatus === 'deleted' ? 'bg-[#eb6309] text-white' : 'text-[#eb6309]'} hover:bg-[#eb6309] hover:text-white`}
                                    onClick={() => {
                                        setFilterStatus('deleted');
                                        setDropdownOpen(false);
                                    }}
                                >
                                    Deleted Specialists
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <input type="text" placeholder="Search" value={search} onChange={(e) => setSearch(e.target.value)} className="form-input !w-1/4 text-left" />
            </div>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="py-3 px-4 text-start font-bold text-lg">Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Email</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Username</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Status</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            // Only show skeleton rows in table body
                            <>
                                {Array.from({ length: itemsPerPage }).map((_, idx) => (
                                    <tr key={idx} className={`animate-pulse ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                                        {TABLE_COL_WIDTHS.map((width, colIdx) => (
                                            <td key={colIdx} className={`py-3 px-4 ${width}`}>
                                                <div className="h-4 bg-gray-200 rounded w-full" />
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </>
                        ) : getPaginatedData().length === 0 ? (
                            <tr>
                                <td colSpan={6} className="py-8 px-4 text-center text-gray-500 text-lg">
                                    <div className="flex flex-col items-center justify-center">No records found</div>
                                </td>
                            </tr>
                        ) : (
                            getPaginatedData().map((row: Specialist, idx: number) => (
                                <tr key={row.id} className={`hover:bg-gray-50 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                                    <td className="py-3 px-4 text-sm text-gray-800">
                                        {row.first_name} {row.last_name}
                                    </td>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.email}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.username}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">
                                        <button
                                            onClick={() => confirmToggleStatus(row)}
                                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                                                row.is_active ? 'bg-[#f36f22] focus:ring-[#f36f22]' : 'bg-gray-200 focus:ring-gray-500'
                                            }`}
                                            title={row.is_active ? 'Deactivate specialist' : 'Activate specialist'}
                                        >
                                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${row.is_active ? 'translate-x-6' : 'translate-x-1'}`} />
                                        </button>
                                    </td>
                                    <td className="py-3 px-4 text-sm text-gray-800">
                                        <div className="flex gap-3 items-center">
                                            {filterStatus !== 'deleted' && (
                                                <>
                                                    <button onClick={() => handleView(row.id)} title="View">
                                                        <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                                                    </button>
                                                    <button onClick={() => handleEdit(row.id)} title="Edit">
                                                        <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
                                                    </button>
                                                    <button onClick={() => confirmDelete(row.id)} title="Delete">
                                                        <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
                                                    </button>
                                                </>
                                            )}
                                            {filterStatus === 'deleted' && (
                                                <button
                                                    onClick={() => confirmRestore(row.id)}
                                                    title="Restore Specialist"
                                                    className="text-green-500 hover:text-green-700 text-xs bg-green-100 px-2 py-1 rounded"
                                                >
                                                    Restore
                                                </button>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
            <Pagination currentPage={currentPage} totalItems={totalItems} itemsPerPage={itemsPerPage} onPageChange={setCurrentPage} onItemsPerPageChange={setItemsPerPage} />
            {/* Restore Confirmation Modal */}
            <Transition appear show={showRestoreModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowRestoreModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>
                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        Confirm Restoration
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">Are you sure you want to restore this specialist?</div>
                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={() => setShowRestoreModal(false)}>
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700" onClick={handleRestore}>
                                            Restore
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
            {/* Toggle Status Confirmation Modal */}
            <Transition appear show={showToggleModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowToggleModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>
                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        {selectedSpecialist?.is_active ? 'Deactivate' : 'Activate'} Specialist
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">
                                        Are you sure you want to {selectedSpecialist?.is_active ? 'deactivate' : 'activate'} {selectedSpecialist?.first_name} {selectedSpecialist?.last_name}?
                                    </div>
                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={() => setShowToggleModal(false)} disabled={isToggling}>
                                            Cancel
                                        </button>
                                        <button
                                            className={`px-4 py-2 text-white rounded ${
                                                selectedSpecialist?.is_active ? 'bg-[#f36f22] hover:bg-[#8b4012]' : 'bg-green-600 hover:bg-green-700'
                                            } ${isToggling ? 'opacity-50 cursor-not-allowed' : ''}`}
                                            onClick={handleToggleStatus}
                                            disabled={isToggling}
                                        >
                                            {isToggling ? (selectedSpecialist?.is_active ? 'Deactivating...' : 'Activating...') : selectedSpecialist?.is_active ? 'Deactivate' : 'Activate'}
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>
                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        Confirm Deletion
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">Are you sure you want to delete this specialist?</div>
                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={() => setShowModal(false)}>
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-[#eb6309] text-white rounded hover:bg-[#fee2e2] hover:text-[#eb6309]" onClick={handleDelete}>
                                            Delete
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default SpecialistList;
