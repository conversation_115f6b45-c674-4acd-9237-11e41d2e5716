'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { Specialist, CreateSpecialistInput, viewSpecialist, createSpecialist, updateSpecialist, getRoles, Role } from '@/src/api/specialist';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';

interface SpecialistModuleProps {
    mode?: 'add' | 'edit' | 'view';
    id?: string;
}

const SpecialistModule: React.FC<SpecialistModuleProps> = ({ mode = 'add', id }) => {
    const router = useRouter();
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [specialist, setSpecialist] = useState<Specialist | null>(null);
    const [roleId, setRoleId] = useState<number | null>(null);

    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        reset,
    } = useForm<CreateSpecialistInput>({
        defaultValues: {
            first_name: '',
            last_name: '',
            email: '',
            username: '',
            role_id: undefined,
        },
    });

    useEffect(() => {
        if ((mode === 'edit' || mode === 'view') && id) {
            setLoading(true);
            viewSpecialist(Number(id))
                .then((d) => {
                    setSpecialist(d);
                    if (mode === 'edit') {
                        setValue('first_name', d.first_name);
                        setValue('last_name', d.last_name);
                        setValue('email', d.email);
                        setValue('username', d.username);
                    }
                })
                .catch((err: any) => {
                    setError(err.message);
                    toast.error(err.message || 'Failed to load specialist details');
                    setTimeout(() => router.push('/specialist'), 2000);
                })
                .finally(() => setLoading(false));
        }

        // Fetch roles and set specialist role_id
        if (mode === 'add') {
            getRoles()
                .then((roles: Role[]) => {
                    const specialistRole = roles.find((r) => r.role_name.toLowerCase() === 'specialist');
                    if (specialistRole) {
                        setRoleId(specialistRole.id);
                        setValue('role_id', specialistRole.id);
                    } else {
                        setError('Specialist role not found');
                    }
                })
                .catch((err: any) => {
                    setError(err.message || 'Failed to fetch roles');
                });
        }
    }, [mode, id, router, setValue]);

    const onSubmit = async (data: CreateSpecialistInput) => {
        setError(null);
        setLoading(true);

        try {
            if (mode === 'add') {
                // Always use the fetched roleId
                await createSpecialist({ ...data, role_id: roleId! });
            } else {
                await updateSpecialist(Number(id), data);
            }
            toast.success(`Specialist ${mode === 'add' ? 'created' : 'updated'} successfully!`);
            router.push('/specialist');
        } catch (err: any) {
            const errorMessage = err?.response?.data?.message || err.message || 'An error occurred';
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => router.push('/specialist');
    const handleEdit = () => router.push(`/specialist/${id}/?mode=edit`);
    const handleBack = () => router.push('/specialist');

    if (loading) {
        return <Loading />;
    }

    if (error && mode === 'view') {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="text-red-600 text-center">
                    <h2 className="text-xl font-bold mb-2">Error</h2>
                    <p>{error}</p>
                    <p>Redirecting to specialist list...</p>
                </div>
            </div>
        );
    }

    if (mode === 'view' && specialist) {
        return (
            <div className="mx-auto card">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Specialist Details</h2>
                    <div className="flex-gap">
                        <button type="button" onClick={handleEdit} className="btn-primary">
                            Edit Specialist
                        </button>
                        <button type="button" onClick={handleBack} className="btn-secondary">
                            Back
                        </button>
                    </div>
                </div>
                <div className="grid-2">
                    <div>
                        <label className="form-label">First Name</label>
                        <div className="form-input bg-disabled">{specialist.first_name}</div>
                    </div>
                    <div>
                        <label className="form-label">Last Name</label>
                        <div className="form-input bg-disabled">{specialist.last_name}</div>
                    </div>
                    <div>
                        <label className="form-label">Email</label>
                        <div className="form-input bg-disabled">{specialist.email}</div>
                    </div>
                    <div>
                        <label className="form-label">Username</label>
                        <div className="form-input bg-disabled">{specialist.username}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mx-auto card">
            <h2 className="text-2xl font-bold mb-6">{mode === 'add' ? 'Add Specialist' : 'Edit Specialist'}</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid-2">
                    {/* First Name */}
                    <div>
                        <label className="form-label">First Name</label>
                        <Controller
                            name="first_name"
                            control={control}
                            rules={{ required: 'First name is required' }}
                            render={({ field }) => <input {...field} type="text" placeholder="Enter first name" className="form-input" disabled={loading} />}
                        />
                        {errors.first_name && <p className="form-error">{errors.first_name.message}</p>}
                    </div>
                    {/* Last Name */}
                    <div>
                        <label className="form-label">Last Name</label>
                        <Controller
                            name="last_name"
                            control={control}
                            rules={{ required: 'Last name is required' }}
                            render={({ field }) => <input {...field} type="text" placeholder="Enter last name" className="form-input" disabled={loading} />}
                        />
                        {errors.last_name && <p className="form-error">{errors.last_name.message}</p>}
                    </div>
                    {/* Email */}
                    <div>
                        <label className="form-label">Email</label>
                        <Controller
                            name="email"
                            control={control}
                            rules={{
                                required: 'Email is required',
                                pattern: {
                                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                    message: 'Invalid email address',
                                },
                            }}
                            render={({ field }) => <input {...field} type="email" placeholder="Enter email" className="form-input" disabled={loading} />}
                        />
                        {errors.email && <p className="form-error">{errors.email.message}</p>}
                    </div>
                    {/* Username */}
                    {mode === 'view' && (
                        <div>
                            <label className="form-label">Username</label>
                            <Controller
                                name="username"
                                control={control}
                                render={({ field }) => <input {...field} type="text" placeholder="Enter username" className="form-input" disabled={true} />}
                            />
                            {errors.username && <p className="form-error">{errors.username.message}</p>}
                        </div>
                    )}
                </div>
                <div className="flex-gap pt-4">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : mode === 'add' ? 'Create Specialist' : 'Update Specialist'}
                    </button>
                    <button type="button" onClick={handleCancel} disabled={loading} className="btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default SpecialistModule;
