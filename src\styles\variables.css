/**
 * CSS Custom Properties for consistent styling across the application
 */

:root {
    /* Brand Colors */
    --color-primary: #eb6309;
    --color-primary-hover: #f36e22;
    --color-primary-light: #fef3e8;

    /* Neutral Colors */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    /* Semantic Colors */
    --color-success: #10b981;
    --color-success-light: #d1fae5;
    --color-error: #ef4444;
    --color-error-light: #fee2e2;
    --color-warning: #f59e0b;
    --color-warning-light: #fef3c7;
    --color-info: #3b82f6;
    --color-info-light: #dbeafe;

    /* Button Colors */
    --color-button-primary: var(--color-primary);
    --color-button-primary-hover: var(--color-primary-hover);
    --color-button-secondary: #7b7b7b;
    --color-button-secondary-hover: #ebebeb;
    --color-button-text-primary: #ffffff;
    --color-button-text-secondary: #444443;

    /* Form Colors */
    --color-input-border: var(--color-gray-300);
    --color-input-focus: var(--color-primary);
    --color-input-bg: #ffffff;
    --color-input-disabled: var(--color-gray-50);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
}

/* Utility Classes */
.btn-primary {
    background-color: var(--color-button-primary);
    color: var(--color-button-text-primary);
    border: 1px solid var(--color-button-primary);
    padding: var(--spacing-xs) var(--spacing-lg);
    border-radius: var(--radius-full);
    transition: all 0.2s ease-in-out;
    font-weight: var(--font-weight-medium);
}

.btn-primary:hover {
    background-color: var(--color-primary-light);
    color: var(--color-button-primary-hover);
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-button-text-secondary);
    border: 1px solid var(--color-button-secondary);
    padding: var(--spacing-xs) var(--spacing-lg);
    border-radius: var(--radius-full);
    transition: all 0.2s ease-in-out;
    font-weight: var(--font-weight-medium);
}

.btn-secondary:hover {
    background-color: var(--color-button-secondary-hover);
}

.form-input {
    width: 100%;
    border: 1px solid var(--color-input-border);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--color-input-bg);
    transition: border-color 0.2s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-input-focus);
    box-shadow: 0 0 0 2px rgb(235 99 9 / 0.2);
}

.form-input:disabled {
    background-color: var(--color-input-disabled);
    cursor: not-allowed;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-700);
}

.form-error {
    font-size: var(--font-size-sm);
    color: var(--color-error);
    margin-top: var(--spacing-xs);
}

.card {
    background-color: #ffffff;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.flex-gap {
    display: flex;
    gap: var(--spacing-md);
}

.text-error {
    color: var(--color-error);
}

.text-success {
    color: var(--color-success);
}

.bg-disabled {
    background-color: var(--color-input-disabled);
}
